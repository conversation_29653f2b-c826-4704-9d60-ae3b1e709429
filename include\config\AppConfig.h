#pragma once

#include <string>

namespace octane {
namespace config {

enum class AppMode {
    DEVELOPMENT,
    PRODUCTION
};

class AppConfig
{
public:
    static AppConfig& getInstance();
    
    // Mode configuration
    void setMode(AppMode mode) { m_mode = mode; }
    AppMode getMode() const { return m_mode; }
    bool isDevelopmentMode() const { return m_mode == AppMode::DEVELOPMENT; }
    bool isProductionMode() const { return m_mode == AppMode::PRODUCTION; }
    
    // Security configuration
    bool isSecurityEnabled() const { return isProductionMode(); }
    bool shouldReportToDiscord() const { return isProductionMode(); }
    
    // License configuration
    bool requiresLicense() const { return isProductionMode(); }
    
    // Debug configuration
    bool isDebugLoggingEnabled() const { return isDevelopmentMode(); }
    
    // Configuration loading/saving
    void loadFromFile(const std::string& configPath = "");
    void saveToFile(const std::string& configPath = "") const;
    
private:
    AppConfig() : m_mode(AppMode::DEVELOPMENT) {} // Default to development mode
    
    AppMode m_mode;
    std::string getDefaultConfigPath() const;
};

} // namespace config
} // namespace octane
