#pragma once
#include <string>

namespace octane {
namespace models {

/**
 * @brief Perfect recoil settings based on working template
 */
class RecoilSettings {
public:
    RecoilSettings()
        : m_sensitivity(1.0f)        // Default to 1.0 as requested
        , m_adsSensitivity(1.0f)     // Default to 1.0 as requested
        , m_fov(90.0f)
        , m_recoilCompensation(100.0f)
        , m_horizontalMultiplier(1.0f)
        , m_verticalMultiplier(1.0f)
        , m_smoothing(50.0f)
        , m_hipfireEnabled(true)
        , m_adsEnabled(true)
    {}

    ~RecoilSettings() = default;

    // Core sensitivity settings (from template)
    float getSensitivity() const { return m_sensitivity; }
    void setSensitivity(float sensitivity) { m_sensitivity = sensitivity; }

    float getAdsSensitivity() const { return m_adsSensitivity; }
    void setAdsSensitivity(float adsSensitivity) { m_adsSensitivity = adsSensitivity; }

    float getFOV() const { return m_fov; }
    void setFOV(float fov) { m_fov = fov; }

    // Recoil compensation percentage (0-100%)
    float getRecoilCompensation() const { return m_recoilCompensation; }
    void setRecoilCompensation(float compensation) { m_recoilCompensation = compensation; }

    // Multipliers for fine-tuning
    float getHorizontalMultiplier() const { return m_horizontalMultiplier; }
    void setHorizontalMultiplier(float multiplier) { m_horizontalMultiplier = multiplier; }

    float getVerticalMultiplier() const { return m_verticalMultiplier; }
    void setVerticalMultiplier(float multiplier) { m_verticalMultiplier = multiplier; }

    // Smoothing and control options
    float getSmoothing() const { return m_smoothing; }
    void setSmoothing(float smoothing) { m_smoothing = smoothing; }

    bool isHipfireEnabled() const { return m_hipfireEnabled; }
    void setHipfireEnabled(bool enabled) { m_hipfireEnabled = enabled; }

    bool isAdsEnabled() const { return m_adsEnabled; }
    void setAdsEnabled(bool enabled) { m_adsEnabled = enabled; }

    // Config save/load system
    std::string getComPort() const { return m_comPort; }
    void setComPort(const std::string& port) { m_comPort = port; }

    bool isAutoLoadConfig() const { return m_autoLoadConfig; }
    void setAutoLoadConfig(bool autoLoad) { m_autoLoadConfig = autoLoad; }

    std::string getConfigName() const { return m_configName; }
    void setConfigName(const std::string& name) { m_configName = name; }

    /**
     * @brief Calculate perfect recoil multiplier using template's EXACT formula
     * TEMPLATE FORMULA: -0.03 * (sensitivity * ads_sensitivity * 3.0) * (fov / 100.0)
     * This is the EXACT formula from the working template line 317
     */
    float calculateRecoilMultiplier(bool isADS = false) const {
        // EXACT TEMPLATE FORMULA from line 317
        // Template: scale = -0.03f * (sensitivity * adsSensitivity * 3.0f) * (fieldOfView / 100.0f);
        // This is the EXACT formula that works in the template
        return -0.03f * (m_sensitivity * m_adsSensitivity * 3.0f) * (m_fov / 100.0f);
    }

private:
    float m_sensitivity;
    float m_adsSensitivity;
    float m_fov;
    float m_recoilCompensation;
    float m_horizontalMultiplier;
    float m_verticalMultiplier;
    float m_smoothing;
    bool m_hipfireEnabled;
    bool m_adsEnabled;

    // Config system
    std::string m_comPort;
    bool m_autoLoadConfig = false;
    std::string m_configName = "Default";
};

} // namespace models
} // namespace octane
