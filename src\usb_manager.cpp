#include "usb_manager.h"
#include "USB.h"
#include "USBCDC.h"

// Global instance
USBManager usbManager;
USBHIDMouse Mouse;
USBCDC USBSerial;

USBManager::USBManager() : usbReady(false), hidReady(false), cdcReady(false) {
}

void USBManager::begin() {
    initializeUSB();
    waitForUSBReady();
}

bool USBManager::isReady() const {
    return usbReady;
}

bool USBManager::isHidReady() const {
    return hidReady;
}

bool USBManager::isCdcReady() const {
    return cdcReady;
}

void USBManager::sendMouseMove(int16_t x, int16_t y) {
    if (hidReady) {
        Mouse.move(x, y);
    }
}

void USBManager::sendMouseClick(uint8_t button, bool pressed) {
    if (hidReady) {
        if (pressed) {
            Mouse.press(button);
        } else {
            Mouse.release(button);
        }
    }
}

void USBManager::sendDebugMessage(const String& message) {
    if (cdcReady) {
        USBSerial.println(message);
    }
}

void USBManager::initializeUSB() {
    // Configure USB device information
    USB.productName("Octane ESP32 HID Device");
    USB.manufacturerName("Octane Team");
    USB.serialNumber("ESP32S2-001");

    // Initialize HID Mouse
    Mouse.begin();
    hidReady = true;

    // Initialize USB CDC Serial
    USBSerial.begin(115200);
    cdcReady = true;

    // Initialize USB (this enables both HID and CDC)
    USB.begin();

    sendDebugMessage("USB HID Mouse + CDC Serial initialized");
    sendDebugMessage("Device should appear as both mouse and COM port");
}

void USBManager::waitForUSBReady() {
    // Wait for USB to be ready (max 5 seconds)
    uint32_t startTime = millis();
    while (!USB && (millis() - startTime < 5000)) {
        delay(10);
    }
    
    usbReady = true;
    cdcReady = true; // Assume CDC is ready when USB is ready
    
    sendDebugMessage("USB initialization complete");
    sendDebugMessage("Ready for HID and CDC communication");
}
