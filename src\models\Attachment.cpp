#include "models/Attachment.h"

namespace octane {
namespace models {

Attachment::Attachment() 
    : m_name(""), m_id(0), m_recoilModifier(), m_repeatDelayModifier(),
      m_fovBias(0.0f), m_fovOffset(0.0f), m_zoomFactor(0.0f) {
}

Attachment::Attachment(const std::string& name, int id, 
                       const Modifier& recoilMod, const Modifier& repeatDelayMod,
                       float fovBias, float fovOffset, float zoomFactor)
    : m_name(name), m_id(id), m_recoilModifier(recoilMod), m_repeatDelayModifier(repeatDelayMod),
      m_fovBias(fovBias), m_fovOffset(fovOffset), m_zoomFactor(zoomFactor) {
}

} // namespace models
} // namespace octane
