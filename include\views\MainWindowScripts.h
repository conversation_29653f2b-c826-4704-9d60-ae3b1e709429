#pragma once
#include <string>

namespace octane {
namespace views {

/**
 * @brief Contains the JavaScript code for the WebView2 interface
 */
class MainWindowScripts {
public:
    /**
     * @brief Get the JavaScript content for the main window
     */
    static std::string getJavaScriptContent();

    /**
     * @brief Get the calibration system JavaScript
     */
    static std::string getCalibrationScripts();

private:
    MainWindowScripts() = delete; // Static class
};

} // namespace views
} // namespace octane
