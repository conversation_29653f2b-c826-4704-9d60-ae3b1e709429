#pragma once

#include <string>
#include <unordered_map>
#include <windows.h>

namespace octane {
namespace models {

/**
 * @brief Keybind configuration system matching template's approach
 * Based on template's m_global::m_keybinds namespace
 */
class KeybindSettings {
public:
    KeybindSettings();
    
    // Get virtual key codes for movement detection
    int getCrouchKey() const { return m_crouchKey; }
    int getForwardKey() const { return m_forwardKey; }
    int getLeftKey() const { return m_leftKey; }
    int getBackwardKey() const { return m_backwardKey; }
    int getRightKey() const { return m_rightKey; }
    
    // Set keybinds from string names (from UI)
    void setCrouchKey(const std::string& keyName);
    void setForwardKey(const std::string& keyName);
    void setLeftKey(const std::string& keyName);
    void setBackwardKey(const std::string& keyName);
    void setRightKey(const std::string& keyName);
    
    // Get string names for UI display
    std::string getCrouchKeyName() const { return m_crouchKeyName; }
    std::string getForwardKeyName() const { return m_forwardKeyName; }
    std::string getLeftKeyName() const { return m_leftKeyName; }
    std::string getBackwardKeyName() const { return m_backwardKeyName; }
    std::string getRightKeyName() const { return m_rightKeyName; }
    
    // Reset to template defaults
    void resetToDefaults();
    
private:
    // Virtual key codes (for GetAsyncKeyState)
    int m_crouchKey;
    int m_forwardKey;
    int m_leftKey;
    int m_backwardKey;
    int m_rightKey;
    
    // String names for UI
    std::string m_crouchKeyName;
    std::string m_forwardKeyName;
    std::string m_leftKeyName;
    std::string m_backwardKeyName;
    std::string m_rightKeyName;
    
    // Key name to virtual key mapping
    static const std::unordered_map<std::string, int> s_keyNameToVirtualKey;
    
    // Helper methods
    int stringToVirtualKey(const std::string& keyName) const;
    std::string virtualKeyToString(int virtualKey) const;
};

} // namespace models
} // namespace octane
