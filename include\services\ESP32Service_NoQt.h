#pragma once

#include <string>
#include <vector>
#include <memory>
#include <chrono>

namespace octane {
namespace services {

/**
 * @brief ESP32 device information
 */
struct ESP32DeviceInfo {
    std::string portName;
    std::string description;
    std::string manufacturer;
    std::string deviceId;
    std::string firmwareVersion;
    bool isConnected = false;
};

/**
 * @brief ESP32 communication service without Qt dependencies
 */
class ESP32Service
{
public:
    explicit ESP32Service();
    ~ESP32Service();

    /**
     * @brief Initialize the service
     */
    bool initialize();

    /**
     * @brief Scan for available ESP32 devices
     */
    std::vector<ESP32DeviceInfo> scanForDevices();

    /**
     * @brief Connect to ESP32 device
     */
    bool connectToDevice(const std::string& portName);

    /**
     * @brief Disconnect from current device
     */
    void disconnect();

    /**
     * @brief Check if connected to a device
     */
    bool isConnected() const;

    /**
     * @brief Send recoil compensation data
     */
    bool sendRecoilData(float x, float y);

    /**
     * @brief Send mouse movement data
     */
    bool sendMouseMovement(int deltaX, int deltaY);

    /**
     * @brief Start auto-connect mode
     */
    void startAutoConnect();

    /**
     * @brief Stop auto-connect mode
     */
    void stopAutoConnect();

    /**
     * @brief Get firmware version
     */
    std::string getFirmwareVersion();

    /**
     * @brief Update firmware
     */
    bool updateFirmware(const std::string& firmwarePath);

    /**
     * @brief Enable/disable debug mode
     */
    void setDebugMode(bool enabled) { m_debugMode = enabled; }

    /**
     * @brief Get current device info
     */
    const ESP32DeviceInfo& getCurrentDevice() const { return m_currentDevice; }

    /**
     * @brief Get available devices
     */
    const std::vector<ESP32DeviceInfo>& getAvailableDevices() const { return m_availableDevices; }

private:
    bool isESP32Device(const std::string& portName);
    bool sendCommand(const std::string& command);
    void parseDeviceResponse(const std::string& data);
    std::vector<std::string> getAvailableComPorts();

private:
    std::vector<ESP32DeviceInfo> m_availableDevices;
    ESP32DeviceInfo m_currentDevice;
    
    bool m_autoConnect;
    bool m_debugMode;
    bool m_initialized;
    
    // Connection constants
    static const int BAUD_RATE = 115200;
    static const int AUTO_CONNECT_INTERVAL_MS = 3000;
    static const int CONNECTION_TIMEOUT_MS = 5000;
};

} // namespace services
} // namespace octane
