#!/usr/bin/env python3
"""
ESP32 ULTRA-OPTIMIZED Firmware Test
Tests the v2.2.0 optimized firmware for recoil control
"""

import serial
import time
import sys
from datetime import datetime

class OptimizedFirmwareTester:
    def __init__(self, port="COM13", baudrate=115200):
        self.port = port
        self.baudrate = baudrate
        self.ser = None
        
    def connect(self):
        """Connect to ESP32"""
        try:
            print(f"🔌 Connecting to OPTIMIZED ESP32 on {self.port}...")
            self.ser = serial.Serial(self.port, self.baudrate, timeout=1)
            time.sleep(2)
            print("✅ Connected!")
            return True
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    def send_command(self, command):
        """Send command and measure response time"""
        if not self.ser:
            return None
            
        start_time = time.time()
        self.ser.write(f"{command}\n".encode())
        
        # Read response with timeout
        response = ""
        timeout = time.time() + 0.1  # 100ms timeout
        
        while time.time() < timeout:
            if self.ser.in_waiting:
                response = self.ser.read_all().decode('utf-8', errors='ignore').strip()
                break
            time.sleep(0.001)  # 1ms polling
        
        end_time = time.time()
        latency = (end_time - start_time) * 1000
        
        return {
            'command': command,
            'response': response,
            'latency_ms': round(latency, 2),
            'success': bool(response and 'ERROR' not in response)
        }
    
    def test_optimizations(self):
        """Test all optimization features"""
        print("\n🚀 TESTING ULTRA-OPTIMIZED FIRMWARE v2.2.0")
        print("=" * 60)
        
        # Test 1: Basic Communication Speed
        print("\n⚡ Test 1: Communication Speed")
        results = []
        for i in range(10):
            result = self.send_command("PING")
            if result:
                results.append(result)
                status = "✅" if result['success'] else "❌"
                print(f"  {status} PING #{i+1}: {result['latency_ms']:.1f}ms - {result['response'][:20]}")
        
        if results:
            avg_latency = sum(r['latency_ms'] for r in results) / len(results)
            success_rate = sum(1 for r in results if r['success']) / len(results) * 100
            print(f"  📊 Average Latency: {avg_latency:.1f}ms")
            print(f"  📊 Success Rate: {success_rate:.1f}%")
            
            if avg_latency < 10:
                print("  ✅ EXCELLENT: Ultra-low latency achieved!")
            elif avg_latency < 20:
                print("  ✅ GOOD: Low latency achieved!")
            else:
                print("  ⚠️  HIGH: Latency still high")
        
        # Test 2: Mouse Movement Speed
        print("\n🖱️  Test 2: Mouse Movement Speed")
        mouse_commands = [
            "MOUSE_MOVE 0,77",
            "MOUSE_MOVE -11,79",
            "MOUSE_MOVE -22,79",
            "MOUSE_MOVE -29,77",
            "MOUSE_MOVE -37,80"
        ]
        
        mouse_results = []
        for cmd in mouse_commands:
            result = self.send_command(cmd)
            if result:
                mouse_results.append(result)
                status = "✅" if result['success'] else "❌"
                print(f"  {status} {cmd}: {result['latency_ms']:.1f}ms - {result['response']}")
        
        if mouse_results:
            avg_mouse_latency = sum(r['latency_ms'] for r in mouse_results) / len(mouse_results)
            mouse_success_rate = sum(1 for r in mouse_results if r['success']) / len(mouse_results) * 100
            print(f"  📊 Mouse Avg Latency: {avg_mouse_latency:.1f}ms")
            print(f"  📊 Mouse Success Rate: {mouse_success_rate:.1f}%")
            
            if mouse_success_rate > 95:
                print("  ✅ EXCELLENT: Mouse commands working perfectly!")
            elif mouse_success_rate > 80:
                print("  ✅ GOOD: Mouse commands mostly working!")
            else:
                print("  ❌ POOR: Mouse commands failing")
        
        # Test 3: Rapid Fire Test (Recoil Simulation)
        print("\n🔥 Test 3: Rapid Fire Test (Recoil Simulation)")
        rapid_commands = ["MOUSE_MOVE 0,77", "MOUSE_MOVE -11,79", "MOUSE_MOVE -22,79"] * 5
        
        start_time = time.time()
        rapid_results = []
        
        for i, cmd in enumerate(rapid_commands):
            result = self.send_command(cmd)
            if result:
                rapid_results.append(result)
                if i < 5:  # Show first 5 results
                    status = "✅" if result['success'] else "❌"
                    print(f"  {status} Rapid #{i+1}: {result['latency_ms']:.1f}ms")
            time.sleep(0.01)  # 10ms between commands (like recoil)
        
        total_time = (time.time() - start_time) * 1000
        
        if rapid_results:
            rapid_success_rate = sum(1 for r in rapid_results if r['success']) / len(rapid_results) * 100
            avg_rapid_latency = sum(r['latency_ms'] for r in rapid_results) / len(rapid_results)
            
            print(f"  📊 Total Time: {total_time:.1f}ms for {len(rapid_commands)} commands")
            print(f"  📊 Rapid Success Rate: {rapid_success_rate:.1f}%")
            print(f"  📊 Rapid Avg Latency: {avg_rapid_latency:.1f}ms")
            
            if rapid_success_rate > 95:
                print("  ✅ EXCELLENT: Rapid fire working perfectly!")
            else:
                print("  ⚠️  NEEDS IMPROVEMENT: Some rapid commands failed")
        
        # Test 4: Status Command
        print("\n📊 Test 4: Status Command")
        status_result = self.send_command("STATUS")
        if status_result:
            status = "✅" if status_result['success'] else "❌"
            print(f"  {status} STATUS: {status_result['latency_ms']:.1f}ms")
            print(f"  Response: {status_result['response']}")
        
        # Final Report
        print("\n" + "=" * 60)
        print("📋 OPTIMIZATION TEST REPORT")
        print("=" * 60)
        
        if results and mouse_results and rapid_results:
            overall_latency = (avg_latency + avg_mouse_latency + avg_rapid_latency) / 3
            overall_success = (success_rate + mouse_success_rate + rapid_success_rate) / 3
            
            print(f"📊 Overall Average Latency: {overall_latency:.1f}ms")
            print(f"📊 Overall Success Rate: {overall_success:.1f}%")
            
            print(f"\n🎯 OPTIMIZATION STATUS:")
            if overall_latency < 10 and overall_success > 95:
                print(f"  ✅ ULTRA-OPTIMIZED: Firmware is working perfectly!")
                print(f"  ✅ Ready for recoil control!")
            elif overall_latency < 20 and overall_success > 80:
                print(f"  ✅ OPTIMIZED: Firmware is working well!")
                print(f"  ✅ Should work for recoil control!")
            else:
                print(f"  ⚠️  NEEDS WORK: Firmware needs more optimization!")
                print(f"  ⚠️  May not work well for recoil control!")
            
            print(f"\n💡 RECOMMENDATIONS:")
            if overall_latency > 20:
                print(f"  - Check ESP32 connection")
                print(f"  - Verify firmware upload")
                print(f"  - Test different USB port")
            if overall_success < 90:
                print(f"  - Check serial communication")
                print(f"  - Verify ESP32 is not overloaded")
                print(f"  - Test with slower command rate")
        
        print(f"\n⏸️  Test complete. Press Enter to exit...")
        input()

def main():
    port = "COM13"
    if len(sys.argv) > 1:
        port = sys.argv[1]
    
    tester = OptimizedFirmwareTester(port)
    if tester.connect():
        tester.test_optimizations()
    else:
        print("Failed to connect. Press Enter to exit...")
        input()

if __name__ == "__main__":
    main()
