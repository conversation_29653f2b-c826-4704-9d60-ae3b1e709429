#pragma once

#include "imgui.h"
#include <memory>
#include <string>
#include <vector>

namespace octane {

// Forward declarations
namespace core {
    class Application;
}

namespace services {
    class ESP32Service;
    class RecoilControlService;
    class AuthenticationService;
}

namespace models {
    class Weapon;
    class RecoilSettings;
}

namespace views {

/**
 * @brief Main application window using ImGui
 */
class MainWindow
{
public:
    MainWindow();
    ~MainWindow();

    /**
     * @brief Render the main window
     */
    void render();

    /**
     * @brief Initialize the window
     */
    bool initialize();

    /**
     * @brief Set the application instance
     */
    void setApplication(core::Application* app) { m_application = app; }

private:
    // Rendering methods
    void renderMenuBar();
    void renderMainContent();
    void renderWeaponSelector();
    void renderRecoilSettings();
    void renderESP32Connection();
    void renderStatusPanel();
    void renderLicensePanel();
    void renderDebugPanel();

    // UI State
    bool m_showDemoWindow = false;
    bool m_showDebugWindow = false;
    bool m_showAboutWindow = false;
    bool m_showLicenseWindow = false;
    
    // Application data
    core::Application* m_application = nullptr;
    
    // UI Values
    int m_selectedWeaponIndex = 0;
    float m_compensationPercentage = 100.0f;
    float m_humanizationPercentage = 0.0f;
    float m_horizontalMultiplier = 100.0f;
    float m_verticalMultiplier = 100.0f;
    float m_smoothingPercentage = 50.0f;
    
    bool m_recoilEnabled = true;
    bool m_adsEnabled = true;
    bool m_crouchEnabled = true;
    
    // ESP32 connection
    int m_selectedPortIndex = 0;
    bool m_esp32Connected = false;
    std::string m_esp32Status = "Disconnected";
    
    // License info
    std::string m_licenseKey = "";
    std::string m_licenseStatus = "Not validated";
    bool m_isAuthenticated = false;
    
    // Available weapons list
    std::vector<std::string> m_weaponNames;
    std::vector<std::string> m_availablePorts;
    
    // Window flags
    ImGuiWindowFlags m_mainWindowFlags = ImGuiWindowFlags_MenuBar | ImGuiWindowFlags_NoDocking;
};

} // namespace views
} // namespace octane
