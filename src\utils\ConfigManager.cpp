#include "utils/ConfigManager.h"
#include <iostream>
#include <sstream>
#include <filesystem>
#include <windows.h>

namespace octane {
namespace utils {

ConfigManager::ConfigManager() {
    // Set config directory to %APPDATA%/OctaneRecoilController
    char* appDataPath = nullptr;
    size_t len = 0;
    if (_dupenv_s(&appDataPath, &len, "APPDATA") == 0 && appDataPath != nullptr) {
        m_configDirectory = std::string(appDataPath) + "\\OctaneRecoilController";
        free(appDataPath);
    } else {
        m_configDirectory = ".\\configs";
    }
    
    m_settingsFile = m_configDirectory + "\\settings.ini";
    ensureConfigDirectory();
}

void ConfigManager::ensureConfigDirectory() {
    try {
        std::filesystem::create_directories(m_configDirectory);
    } catch (const std::exception& e) {
        std::cerr << "Failed to create config directory: " << e.what() << std::endl;
    }
}

bool ConfigManager::saveConfig(const models::RecoilSettings& settings, const std::string& configName) {
    try {
        std::string filePath = getConfigFilePath(configName);
        std::ofstream file(filePath);
        
        if (!file.is_open()) {
            std::cerr << "Failed to open config file for writing: " << filePath << std::endl;
            return false;
        }
        
        file << settingsToString(settings);
        file.close();
        
        std::cout << "Config saved: " << configName << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error saving config: " << e.what() << std::endl;
        return false;
    }
}

bool ConfigManager::loadConfig(models::RecoilSettings& settings, const std::string& configName) {
    try {
        std::string filePath = getConfigFilePath(configName);
        std::ifstream file(filePath);
        
        if (!file.is_open()) {
            std::cerr << "Config file not found: " << filePath << std::endl;
            return false;
        }
        
        std::stringstream buffer;
        buffer << file.rdbuf();
        file.close();
        
        bool success = stringToSettings(buffer.str(), settings);
        if (success) {
            std::cout << "Config loaded: " << configName << std::endl;
        }
        return success;
    } catch (const std::exception& e) {
        std::cerr << "Error loading config: " << e.what() << std::endl;
        return false;
    }
}

std::vector<std::string> ConfigManager::getAvailableConfigs() {
    std::vector<std::string> configs;
    
    try {
        for (const auto& entry : std::filesystem::directory_iterator(m_configDirectory)) {
            if (entry.is_regular_file() && entry.path().extension() == ".cfg") {
                std::string filename = entry.path().stem().string();
                configs.push_back(filename);
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "Error reading config directory: " << e.what() << std::endl;
    }
    
    return configs;
}

bool ConfigManager::deleteConfig(const std::string& configName) {
    try {
        std::string filePath = getConfigFilePath(configName);
        if (std::filesystem::remove(filePath)) {
            std::cout << "Config deleted: " << configName << std::endl;
            return true;
        }
        return false;
    } catch (const std::exception& e) {
        std::cerr << "Error deleting config: " << e.what() << std::endl;
        return false;
    }
}

bool ConfigManager::saveComPort(const std::string& comPort) {
    try {
        std::ofstream file(m_settingsFile);
        if (!file.is_open()) return false;
        
        file << "COM_PORT=" << comPort << std::endl;
        file.close();
        
        std::cout << "COM port saved: " << comPort << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error saving COM port: " << e.what() << std::endl;
        return false;
    }
}

std::string ConfigManager::loadComPort() {
    try {
        std::ifstream file(m_settingsFile);
        if (!file.is_open()) return "";
        
        std::string line;
        while (std::getline(file, line)) {
            if (line.find("COM_PORT=") == 0) {
                return line.substr(9); // Remove "COM_PORT="
            }
        }
        file.close();
    } catch (const std::exception& e) {
        std::cerr << "Error loading COM port: " << e.what() << std::endl;
    }
    
    return "";
}

bool ConfigManager::saveAutoLoadConfig(const std::string& configName, bool enabled) {
    try {
        // Read existing settings
        std::vector<std::string> lines;
        std::ifstream inFile(m_settingsFile);
        std::string line;
        bool foundAutoLoad = false;
        
        while (std::getline(inFile, line)) {
            if (line.find("AUTO_LOAD_CONFIG=") == 0) {
                if (enabled) {
                    lines.push_back("AUTO_LOAD_CONFIG=" + configName);
                } else {
                    lines.push_back("AUTO_LOAD_CONFIG=");
                }
                foundAutoLoad = true;
            } else {
                lines.push_back(line);
            }
        }
        inFile.close();
        
        if (!foundAutoLoad) {
            if (enabled) {
                lines.push_back("AUTO_LOAD_CONFIG=" + configName);
            } else {
                lines.push_back("AUTO_LOAD_CONFIG=");
            }
        }
        
        // Write back to file
        std::ofstream outFile(m_settingsFile);
        if (!outFile.is_open()) return false;
        
        for (const auto& l : lines) {
            outFile << l << std::endl;
        }
        outFile.close();
        
        std::cout << "Auto-load config " << (enabled ? "enabled" : "disabled") << ": " << configName << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error saving auto-load config: " << e.what() << std::endl;
        return false;
    }
}

std::pair<std::string, bool> ConfigManager::loadAutoLoadConfig() {
    try {
        std::ifstream file(m_settingsFile);
        if (!file.is_open()) return {"", false};
        
        std::string line;
        while (std::getline(file, line)) {
            if (line.find("AUTO_LOAD_CONFIG=") == 0) {
                std::string configName = line.substr(17); // Remove "AUTO_LOAD_CONFIG="
                bool enabled = !configName.empty();
                return {configName, enabled};
            }
        }
        file.close();
    } catch (const std::exception& e) {
        std::cerr << "Error loading auto-load config: " << e.what() << std::endl;
    }
    
    return {"", false};
}

std::string ConfigManager::getConfigFilePath(const std::string& configName) {
    return m_configDirectory + "\\" + configName + ".cfg";
}

std::string ConfigManager::settingsToString(const models::RecoilSettings& settings) {
    std::stringstream ss;
    ss << "SENSITIVITY=" << settings.getSensitivity() << std::endl;
    ss << "ADS_SENSITIVITY=" << settings.getAdsSensitivity() << std::endl;
    ss << "FOV=" << settings.getFOV() << std::endl;
    ss << "RECOIL_COMPENSATION=" << settings.getRecoilCompensation() << std::endl;
    ss << "HORIZONTAL_MULTIPLIER=" << settings.getHorizontalMultiplier() << std::endl;
    ss << "VERTICAL_MULTIPLIER=" << settings.getVerticalMultiplier() << std::endl;
    ss << "SMOOTHING=" << settings.getSmoothing() << std::endl;
    ss << "HIPFIRE_ENABLED=" << (settings.isHipfireEnabled() ? "1" : "0") << std::endl;
    ss << "ADS_ENABLED=" << (settings.isAdsEnabled() ? "1" : "0") << std::endl;
    return ss.str();
}

bool ConfigManager::stringToSettings(const std::string& data, models::RecoilSettings& settings) {
    std::istringstream iss(data);
    std::string line;
    
    while (std::getline(iss, line)) {
        size_t pos = line.find('=');
        if (pos == std::string::npos) continue;
        
        std::string key = line.substr(0, pos);
        std::string value = line.substr(pos + 1);
        
        try {
            if (key == "SENSITIVITY") {
                settings.setSensitivity(std::stof(value));
            } else if (key == "ADS_SENSITIVITY") {
                settings.setAdsSensitivity(std::stof(value));
            } else if (key == "FOV") {
                settings.setFOV(std::stof(value));
            } else if (key == "RECOIL_COMPENSATION") {
                settings.setRecoilCompensation(std::stof(value));
            } else if (key == "HORIZONTAL_MULTIPLIER") {
                settings.setHorizontalMultiplier(std::stof(value));
            } else if (key == "VERTICAL_MULTIPLIER") {
                settings.setVerticalMultiplier(std::stof(value));
            } else if (key == "SMOOTHING") {
                settings.setSmoothing(std::stof(value));
            } else if (key == "HIPFIRE_ENABLED") {
                settings.setHipfireEnabled(value == "1");
            } else if (key == "ADS_ENABLED") {
                settings.setAdsEnabled(value == "1");
            }
        } catch (const std::exception& e) {
            std::cerr << "Error parsing config value: " << key << "=" << value << std::endl;
        }
    }
    
    return true;
}

} // namespace utils
} // namespace octane
