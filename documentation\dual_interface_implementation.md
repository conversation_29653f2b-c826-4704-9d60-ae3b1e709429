# ESP32-S2 Dual Interface Implementation (COM + HID)

## platformio.ini Configuration
```ini
[env:lolin_s2_mini]
platform = espressif32
board = lolin_s2_mini
framework = arduino
monitor_speed = 115200

; Build flags for USB HID and debug
build_flags =
    -DARDUINO_USB_MODE=1
    -<PERSON>ARD<PERSON>NO_USB_CDC_ON_BOOT=0
    -DCORE_DEBUG_LEVEL=1
    -DBOARD_HAS_PSRAM

; Library dependencies
lib_deps = 
    ESP32-USB-Soft-Host
```

## usb_manager.cpp - Dual Interface Setup
```cpp
#include "usb_manager.h"
#include "USB.h"
#include "USBCDC.h"

// Global instance
USBManager usbManager;
USBHIDMouse Mouse;
USBCDC USBSerial;

void USBManager::begin() {
    initializeUSB();
}

void USBManager::initializeUSB() {
    // Configure USB device information
    USB.productName("Octane ESP32 HID Device");
    USB.manufacturerName("Octane Team");
    USB.serialNumber("ESP32S2-001");
    
    // Initialize HID Mouse
    Mouse.begin();
    hidReady = true;
    
    // Initialize USB CDC Serial
    USBSerial.begin(115200);
    cdcReady = true;
    
    // Initialize USB (this enables both HID and CDC)
    USB.begin();
    
    sendDebugMessage("USB HID Mouse + CDC Serial initialized");
    sendDebugMessage("Device should appear as both mouse and COM port");
}

void USBManager::sendDebugMessage(const String& message) {
    if (cdcReady) {
        USBSerial.println(message);
    }
}

void USBManager::sendMouseMove(int16_t deltaX, int16_t deltaY) {
    if (hidReady) {
        Mouse.move(deltaX, deltaY);
    }
}
```

## main.cpp - Using USBSerial
```cpp
#include <Arduino.h>
#include "config.h"
#include "led_controller.h"
#include "usb_manager.h"
#include "command_processor.h"
#include "system_info.h"

// External USB Serial reference
extern USBCDC USBSerial;

void setup() {
    // Initialize all components
    systemInfo.begin();
    ledController.begin();
    usbManager.begin();
    commandProcessor.begin();
    
    // Print system information
    systemInfo.printSystemInfo();
    
    // Set initial LED state
    ledController.setState(LED_WAITING);
    
    // Send boot sequence messages
    systemInfo.sendBootSequence();
    
    // Set ready state
    ledController.setState(LED_CONNECTED);
    
    USBSerial.println("Octane ESP32-S2 HID Mouse v" + String(FIRMWARE_VERSION) + " Ready");
}

void loop() {
    // Process serial commands
    commandProcessor.processSerialCommands();
    
    // Process command queue
    commandProcessor.processCommandQueue();
    
    // Update LED pattern
    ledController.update();
    
    // Send heartbeat messages
    commandProcessor.sendHeartbeat();
    
    // Small delay to prevent watchdog issues
    delay(1);
}
```

## command_processor.cpp - Serial Command Processing
```cpp
extern USBCDC USBSerial;

void CommandProcessor::processSerialCommands() {
    while (USBSerial.available()) {
        char c = USBSerial.read();
        
        if (c == '\n' || c == '\r') {
            if (commandBuffer.length() > 0) {
                processCommand(commandBuffer);
                commandBuffer = "";
            }
        } else if (commandBuffer.length() < MAX_COMMAND_LENGTH) {
            commandBuffer += c;
        }
    }
}
```

## Key Points:
- ARDUINO_USB_MODE=1 enables USB functionality
- ARDUINO_USB_CDC_ON_BOOT=0 allows manual CDC control
- USBSerial creates COM port interface
- Mouse creates HID interface
- Both work simultaneously
- Commands via COM port, mouse movements via HID
