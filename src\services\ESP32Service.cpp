#include "services/ESP32Service.h"
#include <iostream>
#include <windows.h>
#include <winreg.h>
#include <thread>
#include <chrono>

namespace octane {
namespace services {

ESP32Service::ESP32Service() : m_connected(false) {
    std::cout << "ESP32Service: Constructor called" << std::endl;
}

ESP32Service::~ESP32Service() {
    if (m_connected) {
        disconnect();
    }
    std::cout << "ESP32Service: Destructor called" << std::endl;
}

bool ESP32Service::initialize() {
    std::cout << "ESP32Service: Initializing..." << std::endl;
    // TODO: Initialize serial communication libraries
    return true;
}

bool ESP32Service::connectToDevice(const std::string& port) {
    std::cout << "ESP32Service: Connecting to device on port " << port << std::endl;

    // Close existing connection if any
    if (m_connected) {
        disconnect();
    }

    // Open serial port
    if (openSerialPort(port)) {
        m_connected = true;
        m_currentPort = port;
        std::cout << "ESP32Service: Connected successfully to " << port << std::endl;
        return true;
    } else {
        std::cerr << "ESP32Service: Failed to connect to " << port << std::endl;
        return false;
    }
}

void ESP32Service::disconnect() {
    if (m_connected) {
        std::cout << "ESP32Service: Disconnecting from " << m_currentPort << std::endl;

        closeSerialPort();
        m_connected = false;
        m_currentPort.clear();

        std::cout << "ESP32Service: Disconnected successfully" << std::endl;
    }
}

bool ESP32Service::isConnected() const {
    return m_connected;
}

std::vector<std::string> ESP32Service::getAvailablePorts() {
    std::vector<std::string> ports;

    // Use Windows Registry to enumerate COM ports properly
    HKEY hKey;
    LONG result = RegOpenKeyExA(HKEY_LOCAL_MACHINE,
        "HARDWARE\\DEVICEMAP\\SERIALCOMM", 0, KEY_READ, &hKey);

    if (result == ERROR_SUCCESS) {
        DWORD index = 0;
        char valueName[256];
        DWORD valueNameSize = sizeof(valueName);
        char valueData[256];
        DWORD valueDataSize = sizeof(valueData);
        DWORD valueType;

        while (RegEnumValueA(hKey, index, valueName, &valueNameSize,
               NULL, &valueType, (LPBYTE)valueData, &valueDataSize) == ERROR_SUCCESS) {

            if (valueType == REG_SZ) {
                std::string portName(valueData);

                // Test if the port can be opened (not in use)
                std::string portPath = "\\\\.\\" + portName;
                HANDLE hSerial = CreateFileA(
                    portPath.c_str(),
                    GENERIC_READ | GENERIC_WRITE,
                    0,
                    NULL,
                    OPEN_EXISTING,
                    FILE_ATTRIBUTE_NORMAL,
                    NULL
                );

                if (hSerial != INVALID_HANDLE_VALUE) {
                    ports.push_back(portName);
                    CloseHandle(hSerial);
                    std::cout << "ESP32Service: Found available port: " << portName << std::endl;
                } else {
                    std::cout << "ESP32Service: Port " << portName << " exists but is in use" << std::endl;
                }
            }

            // Reset buffer sizes for next iteration
            valueNameSize = sizeof(valueName);
            valueDataSize = sizeof(valueData);
            index++;
        }

        RegCloseKey(hKey);
    } else {
        std::cout << "ESP32Service: Failed to open registry key for COM port enumeration" << std::endl;

        // Fallback: try common COM ports
        for (int i = 1; i <= 20; ++i) {
            std::string port = "COM" + std::to_string(i);
            std::string portPath = "\\\\.\\" + port;

            HANDLE hSerial = CreateFileA(
                portPath.c_str(),
                GENERIC_READ | GENERIC_WRITE,
                0,
                NULL,
                OPEN_EXISTING,
                FILE_ATTRIBUTE_NORMAL,
                NULL
            );

            if (hSerial != INVALID_HANDLE_VALUE) {
                ports.push_back(port);
                CloseHandle(hSerial);
            }
        }
    }

    std::cout << "ESP32Service: Found " << ports.size() << " available COM ports total" << std::endl;
    return ports;
}

bool ESP32Service::sendRecoilData(float x, float y) {
    if (!m_connected) {
        return false;
    }

    // ENHANCED RATE LIMITING - Accumulate movements to reduce ESP32 load
    static float accumulatedX = 0.0f, accumulatedY = 0.0f;
    static auto lastSendTime = std::chrono::steady_clock::now();
    static int skipCount = 0;

    auto now = std::chrono::steady_clock::now();
    auto timeSinceLastSend = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastSendTime).count();

    // Accumulate movements
    accumulatedX += x;
    accumulatedY += y;

    // Only send if accumulated movement is significant OR enough time has passed
    bool shouldSend = false;

    // Send if accumulated movement is significant (>= 1 pixel)
    if (std::abs(accumulatedX) >= 1.0f || std::abs(accumulatedY) >= 1.0f) {
        shouldSend = true;
    }
    // Or if enough time has passed (minimum 15ms between sends)
    else if (timeSinceLastSend >= 15) {
        shouldSend = true;
    }

    if (!shouldSend) {
        skipCount++;
        return true; // Skip but return success
    }

    // Skip very small accumulated movements
    if (std::abs(accumulatedX) < 0.3f && std::abs(accumulatedY) < 0.3f) {
        accumulatedX = 0.0f;
        accumulatedY = 0.0f;
        return true;
    }

    // Convert to integer precision to reduce string processing on ESP32
    int intX = static_cast<int>(std::round(accumulatedX));
    int intY = static_cast<int>(std::round(accumulatedY));

    // Send command with integer precision
    char buffer[32];
    snprintf(buffer, sizeof(buffer), "MOUSE_MOVE %d,%d", intX, intY);
    std::string command(buffer);

    // Reset accumulation and update timing
    accumulatedX = 0.0f;
    accumulatedY = 0.0f;
    lastSendTime = now;

    if (skipCount > 0) {
        std::cout << "ESP32: Sent accumulated movement (" << intX << "," << intY << ") after skipping " << skipCount << " commands" << std::endl;
        skipCount = 0;
    }

    // Send command
    return sendCommand(command);
}

bool ESP32Service::sendCommand(const std::string& command) {
    if (!m_connected) {
        return false;
    }

    // Add newline to command (ESP32 expects line-terminated commands)
    std::string commandWithNewline = command + "\n";
    return writeToSerial(commandWithNewline);
}

bool ESP32Service::sendRecoilSequence(const std::vector<std::pair<float, float>>& movements, int smoothness, int bulletDelay) {
    if (!m_connected || movements.empty()) {
        return false;
    }

    // Build RECOIL_SEQUENCE command: smoothness,delay,x1,y1;x2,y2;x3,y3;...
    std::string command = "RECOIL_SEQUENCE " + std::to_string(smoothness) + "," + std::to_string(bulletDelay) + ",";

    for (size_t i = 0; i < movements.size(); ++i) {
        if (i > 0) command += ";";
        command += std::to_string(static_cast<int>(movements[i].first)) + "," +
                   std::to_string(static_cast<int>(movements[i].second));
    }

    std::cout << "ESP32: Sending optimized recoil sequence (" << movements.size() << " bullets, "
              << smoothness << "% smoothness)" << std::endl;

    // DEBUG: Show the actual command being sent
    std::cout << "DEBUG: Command length: " << command.length() << " characters" << std::endl;
    std::cout << "DEBUG: Full command: " << command << std::endl;

    return sendCommand(command);
}

bool ESP32Service::openSerialPort(const std::string& port) {
    // Convert port name to Windows format (e.g., "COM13" -> "\\\\.\\COM13")
    std::string portPath = "\\\\.\\" + port;

    // Open serial port
    m_serialHandle = CreateFileA(
        portPath.c_str(),
        GENERIC_READ | GENERIC_WRITE,
        0,
        NULL,
        OPEN_EXISTING,
        FILE_ATTRIBUTE_NORMAL,
        NULL
    );

    if (m_serialHandle == INVALID_HANDLE_VALUE) {
        std::cerr << "Failed to open serial port " << port << ". Error: " << GetLastError() << std::endl;
        return false;
    }

    // Configure serial port settings
    DCB dcbSerialParams = {0};
    dcbSerialParams.DCBlength = sizeof(dcbSerialParams);

    if (!GetCommState(m_serialHandle, &dcbSerialParams)) {
        std::cerr << "Failed to get serial port state. Error: " << GetLastError() << std::endl;
        closeSerialPort();
        return false;
    }

    // Set serial port parameters (same as C# application)
    dcbSerialParams.BaudRate = CBR_115200;  // 115200 baud
    dcbSerialParams.ByteSize = 8;           // 8 data bits
    dcbSerialParams.StopBits = ONESTOPBIT;  // 1 stop bit
    dcbSerialParams.Parity = NOPARITY;      // No parity

    if (!SetCommState(m_serialHandle, &dcbSerialParams)) {
        std::cerr << "Failed to set serial port state. Error: " << GetLastError() << std::endl;
        closeSerialPort();
        return false;
    }

    // Set timeouts
    COMMTIMEOUTS timeouts = {0};
    timeouts.ReadIntervalTimeout = 50;
    timeouts.ReadTotalTimeoutConstant = 50;
    timeouts.ReadTotalTimeoutMultiplier = 10;
    timeouts.WriteTotalTimeoutConstant = 50;
    timeouts.WriteTotalTimeoutMultiplier = 10;

    if (!SetCommTimeouts(m_serialHandle, &timeouts)) {
        std::cerr << "Failed to set serial port timeouts. Error: " << GetLastError() << std::endl;
        closeSerialPort();
        return false;
    }

    std::cout << "Serial port " << port << " opened successfully" << std::endl;
    return true;
}

void ESP32Service::closeSerialPort() {
    if (m_serialHandle != INVALID_HANDLE_VALUE) {
        CloseHandle(m_serialHandle);
        m_serialHandle = INVALID_HANDLE_VALUE;
        std::cout << "Serial port closed" << std::endl;
    }
}

bool ESP32Service::writeToSerial(const std::string& data) {
    if (m_serialHandle == INVALID_HANDLE_VALUE) {
        return false;
    }

    // MAXIMUM SPEED SERIAL WRITE - No retries, no delays (template style)
    DWORD bytesWritten;
    BOOL result = WriteFile(
        m_serialHandle,
        data.c_str(),
        static_cast<DWORD>(data.length()),
        &bytesWritten,
        NULL
    );

    if (result && bytesWritten == data.length()) {
        // Success - no debug output for maximum speed
        return true;
    }

    // Only log errors, don't retry (template prioritizes speed over reliability)
    DWORD error = GetLastError();
    if (error != 0) {
        std::cerr << "ESP32 write error: " << error << std::endl;
    }

    return false;
}

} // namespace services
} // namespace octane
