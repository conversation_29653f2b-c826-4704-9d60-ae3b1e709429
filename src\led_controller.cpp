#include "led_controller.h"

// Global instance
LEDController ledController;

LEDController::LEDController() 
    : currentState(LED_BOOT), lastUpdate(0), patternStartTime(0), 
      ledOn(false), blinkCount(0), maxBlinks(0), blinkInterval(100), 
      pauseInterval(1000), inPause(false) {
}

void LEDController::begin() {
    pinMode(LED_PIN, OUTPUT);
    setLED(false); // Start with LED off (active-low: HIGH = OFF, LOW = ON)
    patternStartTime = millis();
    setState(LED_BOOT);
}

void LEDController::setState(LEDState state) {
    if (currentState != state) {
        currentState = state;
        patternStartTime = millis();
        lastUpdate = 0;
        blinkCount = 0;
        inPause = false;
        
        // Configure pattern parameters based on state
        switch (state) {
            case LED_BOOT:
                blinkInterval = 100;
                pauseInterval = 200;
                maxBlinks = 255; // Continuous
                break;
            case LED_WAITING:
                blinkInterval = 1000;
                pauseInterval = 1000;
                maxBlinks = 1;
                break;
            case LED_CONNECTED:
                blinkInterval = 2000;
                pauseInterval = 500;
                maxBlinks = 1;
                break;
            case LED_ACTIVE:
                blinkInterval = 150;
                pauseInterval = 300;
                maxBlinks = 2;
                break;
            case LED_ERROR:
                blinkInterval = 100;
                pauseInterval = 200;
                maxBlinks = 3;
                break;
            case LED_STEALTH:
                blinkInterval = 2000;
                pauseInterval = 2000;
                maxBlinks = 1;
                break;
            case LED_UPDATING:
                blinkInterval = 50;
                pauseInterval = 50;
                maxBlinks = 255; // Continuous
                break;
            case LED_BINDING:
                blinkInterval = 250;
                pauseInterval = 250;
                maxBlinks = 255; // Continuous alternating
                break;
            case LED_AUTHENTICATED:
                blinkInterval = 3000;
                pauseInterval = 1000;
                maxBlinks = 1;
                break;
            case LED_COMMAND_RECEIVED:
                blinkInterval = 50;
                pauseInterval = 0;
                maxBlinks = 1;
                break;
        }
    }
}

void LEDController::update() {
    uint32_t currentTime = millis();
    
    switch (currentState) {
        case LED_BOOT:
            updateBootPattern();
            break;
        case LED_WAITING:
            updateWaitingPattern();
            break;
        case LED_CONNECTED:
            updateConnectedPattern();
            break;
        case LED_ACTIVE:
            updateActivePattern();
            break;
        case LED_ERROR:
            updateErrorPattern();
            break;
        case LED_STEALTH:
            updateStealthPattern();
            break;
        case LED_UPDATING:
            updateUpdatingPattern();
            break;
        case LED_BINDING:
            updateBindingPattern();
            break;
        case LED_AUTHENTICATED:
            updateAuthenticatedPattern();
            break;
        case LED_COMMAND_RECEIVED:
            updateCommandReceivedPattern();
            break;
    }
}

LEDState LEDController::getCurrentState() const {
    return currentState;
}

void LEDController::setLED(bool state) {
    // Active-low LED: HIGH = OFF, LOW = ON
    digitalWrite(LED_PIN, state ? LOW : HIGH);
    ledOn = state;
}

void LEDController::updateBootPattern() {
    uint32_t currentTime = millis();
    
    if (currentTime - lastUpdate >= blinkInterval) {
        setLED(!ledOn);
        lastUpdate = currentTime;
    }
}

void LEDController::updateWaitingPattern() {
    uint32_t currentTime = millis();
    uint32_t elapsed = currentTime - patternStartTime;
    
    // Slow pulse pattern
    if (elapsed < 1000) {
        setLED(true);
    } else if (elapsed < 2000) {
        setLED(false);
    } else {
        patternStartTime = currentTime;
    }
}

void LEDController::updateConnectedPattern() {
    uint32_t currentTime = millis();
    uint32_t elapsed = currentTime - patternStartTime;

    // Slow pulse: 2 seconds on, 0.5 seconds off
    if (elapsed < 2000) {
        setLED(true);
    } else if (elapsed < 2500) {
        setLED(false);
    } else {
        patternStartTime = currentTime;
    }
}

void LEDController::updateActivePattern() {
    uint32_t currentTime = millis();
    
    if (!inPause) {
        if (currentTime - lastUpdate >= blinkInterval) {
            setLED(!ledOn);
            lastUpdate = currentTime;
            
            if (!ledOn) { // Just turned off
                blinkCount++;
                if (blinkCount >= maxBlinks) {
                    inPause = true;
                    blinkCount = 0;
                    lastUpdate = currentTime;
                }
            }
        }
    } else {
        if (currentTime - lastUpdate >= pauseInterval) {
            inPause = false;
            lastUpdate = currentTime;
        }
    }
}

void LEDController::updateErrorPattern() {
    updateActivePattern(); // Same pattern but different timing
}

void LEDController::updateStealthPattern() {
    uint32_t currentTime = millis();
    uint32_t elapsed = currentTime - patternStartTime;
    
    // Very dim pulse - quick on, long off
    if (elapsed < 50) {
        setLED(true);
    } else if (elapsed < 3000) {
        setLED(false);
    } else {
        patternStartTime = currentTime;
    }
}

void LEDController::updateUpdatingPattern() {
    updateBootPattern(); // Same as boot but context is different
}

void LEDController::updateBindingPattern() {
    uint32_t currentTime = millis();
    
    if (currentTime - lastUpdate >= blinkInterval) {
        setLED(!ledOn);
        lastUpdate = currentTime;
    }
}

void LEDController::updateAuthenticatedPattern() {
    uint32_t currentTime = millis();
    uint32_t elapsed = currentTime - patternStartTime;
    
    // Steady glow with occasional dim
    if (elapsed < 3000) {
        setLED(true);
    } else if (elapsed < 3500) {
        setLED(false);
    } else {
        patternStartTime = currentTime;
    }
}

void LEDController::updateCommandReceivedPattern() {
    uint32_t currentTime = millis();
    
    if (currentTime - patternStartTime < 100) {
        setLED(true);
    } else {
        setLED(false);
        // Auto-return to previous state after brief flash
        setState(LED_CONNECTED);
    }
}
