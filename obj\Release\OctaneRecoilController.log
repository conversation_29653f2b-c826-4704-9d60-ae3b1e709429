﻿  WeaponData.cpp
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\algorithm(3769,24): warning C4244: '=': conversion from 'int' to 'char', possible loss of data
  (compiling source file 'src/models/WeaponData.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\algorithm(3769,24):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\src\models\WeaponData.cpp(72,36):
          see reference to function template instantiation '_OutIt std::transform<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,int(__cdecl *)(int)>(const _InIt,const _InIt,_OutIt,_Fn)' being compiled
          with
          [
              _OutIt=std::_String_iterator<std::_String_val<std::_Simple_types<char>>>,
              _Elem=char,
              _InIt=std::_String_iterator<std::_String_val<std::_Simple_types<char>>>,
              _Fn=int (__cdecl *)(int)
          ]
  
  RecoilControlService.cpp
C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\include\models\RecoilSettings.h(72,42): warning C4100: 'isADS': unreferenced formal parameter
  (compiling source file 'src/services/RecoilControlService.cpp')
  
C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\src\services\RecoilControlService.cpp(428,79): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\src\services\RecoilControlService.cpp(432,70): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\src\services\RecoilControlService.cpp(459,18): warning C4189: 'sent': local variable is initialized but not referenced
C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\src\services\RecoilControlService.cpp(803,16): warning C4244: 'initializing': conversion from 'int' to 'char', possible loss of data
  Generating code
  67 of 2373 functions ( 2.8%) were compiled, the rest were copied from previous compilation.
    0 functions were new in current compilation
    2 functions had inline decision re-evaluated but remain unchanged
  Finished generating code
  OctaneRecoilController.vcxproj -> C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\bin\Release\OctaneRecoilController.exe
