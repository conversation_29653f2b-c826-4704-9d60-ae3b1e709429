/*
 * Timing Diagnostic Tool - Debug bullet counting mismatch
 * This tool helps identify timing issues between game and desktop app
 */

#include <iostream>
#include <chrono>
#include <vector>
#include <windows.h>

class TimingDiagnostic {
private:
    std::vector<std::chrono::steady_clock::time_point> buttonPresses;
    std::vector<std::chrono::steady_clock::time_point> bulletTimes;
    std::chrono::steady_clock::time_point lastButtonState;
    bool wasPressed = false;
    int bulletCount = 0;
    int actualClicks = 0;
    
public:
    void startDiagnostic() {
        std::cout << "🔍 TIMING DIAGNOSTIC TOOL" << std::endl;
        std::cout << "=========================" << std::endl;
        std::cout << "Hold LEFT mouse button to simulate firing" << std::endl;
        std::cout << "Press ESC to stop and show results" << std::endl;
        std::cout << std::endl;
        
        auto startTime = std::chrono::steady_clock::now();
        
        while (true) {
            auto now = std::chrono::steady_clock::now();
            bool isPressed = (GetAsyncKeyState(VK_LBUTTON) & 0x8000) != 0;
            bool escPressed = (GetAsyncKeyState(VK_ESCAPE) & 0x8000) != 0;
            
            if (escPressed) {
                break;
            }
            
            // Detect button press changes
            if (isPressed != wasPressed) {
                if (isPressed) {
                    std::cout << "🔥 Mouse button PRESSED at " 
                              << std::chrono::duration_cast<std::chrono::milliseconds>(now - startTime).count() 
                              << "ms" << std::endl;
                    buttonPresses.push_back(now);
                    actualClicks++;
                } else {
                    std::cout << "🔄 Mouse button RELEASED at " 
                              << std::chrono::duration_cast<std::chrono::milliseconds>(now - startTime).count() 
                              << "ms" << std::endl;
                }
                wasPressed = isPressed;
                lastButtonState = now;
            }
            
            // Simulate current app logic for bullet timing
            if (isPressed) {
                simulateCurrentAppLogic(now);
            } else {
                // Reset when not pressed
                bulletCount = 0;
            }
            
            Sleep(1);
        }
        
        showResults(startTime);
    }
    
private:
    void simulateCurrentAppLogic(std::chrono::steady_clock::time_point now) {
        static std::chrono::steady_clock::time_point lastBulletTime = std::chrono::steady_clock::now();
        static bool recoilActive = false;
        static std::chrono::steady_clock::time_point recoilStartTime;
        
        // Current app logic simulation
        auto timeSinceChange = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastButtonState).count();
        if (timeSinceChange < 100) {
            return; // Button state not stable yet (CURRENT APP LOGIC)
        }
        
        if (!recoilActive) {
            recoilActive = true;
            recoilStartTime = now;
            lastBulletTime = now;
            bulletCount = 0;
        }
        
        auto timeSinceLastBullet = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastBulletTime).count();
        int minimumDelay = 150; // CURRENT APP LOGIC - forced 150ms minimum
        
        if (timeSinceLastBullet >= minimumDelay) {
            bulletCount++;
            bulletTimes.push_back(now);
            lastBulletTime = now;
            
            std::cout << "💥 App bullet #" << bulletCount 
                      << " at +" << timeSinceLastBullet << "ms" << std::endl;
        }
    }
    
    void showResults(std::chrono::steady_clock::time_point startTime) {
        std::cout << std::endl;
        std::cout << "📊 DIAGNOSTIC RESULTS" << std::endl;
        std::cout << "=====================" << std::endl;
        
        auto totalTime = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - startTime).count();
        
        std::cout << "Total test duration: " << totalTime << "ms" << std::endl;
        std::cout << "Actual button presses: " << actualClicks << std::endl;
        std::cout << "App counted bullets: " << bulletCount << std::endl;
        std::cout << "Mismatch: " << (actualClicks - bulletCount) << " bullets" << std::endl;
        std::cout << std::endl;
        
        // Calculate actual vs app fire rates
        if (bulletTimes.size() > 1) {
            std::vector<int> intervals;
            for (size_t i = 1; i < bulletTimes.size(); i++) {
                auto interval = std::chrono::duration_cast<std::chrono::milliseconds>(
                    bulletTimes[i] - bulletTimes[i-1]).count();
                intervals.push_back(interval);
            }
            
            int totalInterval = 0;
            for (int interval : intervals) {
                totalInterval += interval;
            }
            int avgInterval = totalInterval / intervals.size();
            
            std::cout << "🎯 TIMING ANALYSIS:" << std::endl;
            std::cout << "Average app bullet interval: " << avgInterval << "ms" << std::endl;
            std::cout << "App fire rate: " << (1000.0 / avgInterval) << " bullets/sec" << std::endl;
            std::cout << std::endl;
        }
        
        // Show identified issues
        std::cout << "⚠️  IDENTIFIED ISSUES:" << std::endl;
        std::cout << "1. 100ms button stability delay causes missed rapid clicks" << std::endl;
        std::cout << "2. 150ms minimum delay is too slow for fast weapons" << std::endl;
        std::cout << "3. App timing doesn't match game's actual fire rate" << std::endl;
        std::cout << std::endl;
        
        // Suggest fixes
        std::cout << "🔧 SUGGESTED FIXES:" << std::endl;
        std::cout << "1. Reduce button stability delay to 10-20ms" << std::endl;
        std::cout << "2. Use actual weapon delay instead of forced minimum" << std::endl;
        std::cout << "3. Add adaptive timing based on actual button press patterns" << std::endl;
        std::cout << "4. Implement game-specific fire rate detection" << std::endl;
    }
};

int main() {
    std::cout << "🚀 Recoil Timing Diagnostic Tool" << std::endl;
    std::cout << "This tool helps identify timing mismatches between game and app" << std::endl;
    std::cout << std::endl;
    
    TimingDiagnostic diagnostic;
    diagnostic.startDiagnostic();
    
    std::cout << std::endl;
    std::cout << "Press any key to exit..." << std::endl;
    std::cin.get();
    
    return 0;
}
