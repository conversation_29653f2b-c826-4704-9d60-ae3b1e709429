#include "views/MainWindowStructure.h"

namespace octane {
namespace views {

std::string MainWindowStructure::getHtmlStructure()
{
    std::string html = R"(
<body>
    <!-- Simple License Modal -->
    <div id='license-modal' class='modal' style='display: flex;'>
        <div class='modal-content license-modal-content'>
            <div class='license-header'>
                <h2>Octane Recoil Scripts v4.2.1</h2>
                <p>Enter your license key to continue</p>
            </div>
            <div class='license-form'>
                <input type='text' id='license-key-input' placeholder='Enter your license key' maxlength='50' autocomplete='off'>
                <div id='license-error' class='error-message'></div>
                <div class='license-buttons'>
                    <button id='validate-btn' onclick='validateLicenseKey()' class='btn-primary'>Activate License</button>
                    <button onclick='exitApp()' class='btn-secondary'>Exit</button>
                </div>
            </div>
            <div class='license-info'>
                <p style='text-align: center;'>Your license key will be saved locally for automatic login</p>
            </div>
        </div>
    </div>

    <div class='container' id='main-container' style='display: none;'>
        <div class='title-bar-compact'>
            <div class='title-compact'>Octane Recoil Scripts v4.2.1</div>
            <div class='license-compact'>
                <span id='license-time'><span id='days-remaining'>--</span> days remaining</span>
            </div>
        </div>

        <main class='main-content'>
            <nav class='tab-nav'>
                <button class='tab-btn active' data-tab='main' onclick='showTab("main")'>Main</button>
                <button class='tab-btn' data-tab='settings' onclick='showTab("settings")'>Settings</button>
                <button class='tab-btn' data-tab='features' onclick='showTab("features")'>Features</button>
                <button class='tab-btn' data-tab='keybinds' onclick='showTab("keybinds")'>Keybinds</button>
                <button class='tab-btn' data-tab='loadouts' onclick='showTab("loadouts")'>Loadouts</button>
                <button class='tab-btn' data-tab='miscellaneous' onclick='showTab("miscellaneous")'>Miscellaneous</button>
            </nav>

            <div class='content-area'>)";
    
    html += getMainTab();
    html += getSettingsTab();
    html += getFeaturesTab();
    html += getKeybindsTab();
    html += getLoadoutsTab();
    html += getMiscellaneousTab();
    
    html += R"(
            </div>
        </main>
    </div>
</body>)";
    
    return html;
}

std::string MainWindowStructure::getMainTab()
{
    return R"(
                <div id='main-tab' class='tab-content active'>
                    <div class='main-grid'>
                        <!-- Top Left: Recoil Control & ESP32 -->
                        <div class='main-card'>
                            <h3>Recoil Control</h3>
                            <div class='control-row'>
                                <button id='recoil-toggle' class='toggle-btn disabled' onclick='toggleRecoil()'>DISABLED</button>
                                <button id='recoil-keybind-btn' class='keybind-btn' onclick='captureRecoilKeybind()' title='Set keybind'>F1</button>
                            </div>
                            <div class='esp32-section'>
                                <div class='port-row'>
                                    <select id='esp32-port-dropdown' class='port-select'>
                                        <option value=''>Select COM port...</option>
                                    </select>
                                    <button onclick='refreshPorts()' class='btn-small'>Refresh</button>
                                </div>
                                <div class='connection-row'>
                                    <button id='esp32-connect-btn' onclick='toggleESP32Connection()' class='connect-btn'>Connect</button>
                                    <div class='status-display'>
                                        <span class='status-dot disconnected' id='esp32-connection-status'></span>
                                        <span id='esp32-connection-text'>Disconnected</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Top Right: Weapon Configuration -->
                        <div class='main-card'>
                            <h3>Weapon Setup</h3>
                            <div class='weapon-row'>
                                <label>Weapon:</label>
                                <select id='primary-weapon' onchange='onWeaponChange()' class='weapon-select'>
                                    <option value='none' selected>None</option>
                                    <option value='ak47u'>AK</option>
                                    <option value='lr300'>LR-300</option>
                                    <option value='m249'>M249</option>
                                    <option value='semi_auto_rifle'>Semi Auto Rifle</option>
                                    <option value='mp5'>MP5</option>
                                    <option value='smg'>Custom SMG</option>
                                    <option value='thompson'>Thompson</option>
                                    <option value='hmlmg'>HMLMG</option>
                                    <option value='python'>Python</option>
                                    <option value='pistol_revolver'>Revolver</option>
                                    <option value='pistol_semiauto'>Semi Auto Pistol</option>
                                    <option value='m92'>M92</option>
                                </select>
                            </div>
                            <div class='weapon-row'>
                                <label>Sight:</label>
                                <select id='sight' onchange='onSightChange()' class='weapon-select'>
                                    <option value='none' selected>None</option>
                                    <option value='iron'>Iron Sights</option>
                                    <option value='holo'>Holo</option>
                                    <option value='red-dot'>Red Dot</option>
                                    <option value='acog'>ACOG</option>
                                    <option value='scope'>8x Scope</option>
                                </select>
                            </div>
                            <div class='weapon-row'>
                                <label>Muzzle:</label>
                                <select id='muzzle' onchange='onMuzzleChange()' class='weapon-select'>
                                    <option value='none' selected>None</option>
                                    <option value='compensator'>Compensator</option>
                                    <option value='flash-hider'>Flash Hider</option>
                                    <option value='muzzle-brake'>Muzzle Brake</option>
                                    <option value='silencer'>Silencer</option>
                                </select>
                            </div>
                        </div>

                        <!-- Bottom Left: Settings -->
                        <div class='main-card'>
                            <h3>Game Settings</h3>
                            <div class='settings-grid'>
                                <div class='setting-item'>
                                    <label class='tooltip' data-tooltip='Your in-game mouse sensitivity setting'>Sensitivity</label>
                                    <input type='number' id='main-sensitivity' min='0.1' max='10' step='0.1' value='1' onchange='onMainSensitivityChange()' class='setting-input'>
                                </div>
                                <div class='setting-item'>
                                    <label class='tooltip' data-tooltip='Your aim-down-sight sensitivity multiplier'>ADS Sensitivity</label>
                                    <input type='number' id='main-ads-sensitivity' min='0.1' max='10' step='0.1' value='1' onchange='onMainAdsSensitivityChange()' class='setting-input'>
                                </div>
                                <div class='setting-item'>
                                    <label class='tooltip' data-tooltip='Your in-game field of view setting (60-120)'>Field of View</label>
                                    <input type='number' id='main-fov' min='60' max='120' value='90' onchange='onMainFovChange()' class='setting-input'>
                                </div>
                                <div class='setting-item'>
                                    <div class='checkbox-row'>
                                        <input type='checkbox' id='compensate-hipfire' onchange='onCompensateHipfireChange()'>
                                        <label for='compensate-hipfire' class='tooltip' data-tooltip='Apply recoil compensation when not aiming down sights'>Compensate Hipfire</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Bottom Right: Humanization -->
                        <div class='main-card'>
                            <h3>Humanization</h3>
                            <div class='humanization-section'>
                                <div class='humanization-row'>
                                    <button id='humanization-toggle' class='humanization-toggle' onclick='toggleHumanization()'>DISABLED</button>
                                    <button id='humanization-keybind-btn' class='humanization-keybind' onclick='captureHumanizationKeybind()' title='Set keybind'>F2</button>
                                </div>
                                <div class='humanization-sliders'>
                                    <div class='humanization-item'>
                                        <label>Randomization</label>
                                        <div class='humanization-slider-group'>
                                            <input type='range' id='humanization-randomization' min='0' max='100' value='50' oninput='onHumanizationRandomizationChange()' class='humanization-slider'>
                                            <input type='number' id='humanization-randomization-input' min='0' max='100' value='50' onchange='onHumanizationRandomizationInputChange()' class='humanization-input'>
                                        </div>
                                    </div>
                                    <div class='humanization-item'>
                                        <label>Humanization</label>
                                        <div class='humanization-slider-group'>
                                            <input type='range' id='humanization-level' min='0' max='100' value='30' oninput='onHumanizationLevelChange()' class='humanization-slider'>
                                            <input type='number' id='humanization-level-input' min='0' max='100' value='30' onchange='onHumanizationLevelInputChange()' class='humanization-input'>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>)";
}

std::string MainWindowStructure::getSettingsTab()
{
    return R"(
                <div id='settings-tab' class='tab-content'>
                    <div class='section-grid'>
                        <div class='section-card'>
                            <h3>Sensitivity Settings</h3>
                            <div class='form-row'>
                                <label class='tooltip' data-tooltip='Your in-game mouse sensitivity setting'>Mouse Sensitivity:</label>
                                <div class='input-group'>
                                    <input type='range' id='sensitivity' min='0.1' max='10' step='0.1' value='1.0' oninput='onSensitivityChange()'>
                                    <input type='number' id='sensitivity-input' min='0.1' max='10' step='0.1' value='1.0' onchange='onSensitivityInputChange()'>
                                </div>
                            </div>
                            <div class='form-row'>
                                <label class='tooltip' data-tooltip='Aim-down-sight sensitivity multiplier'>ADS:</label>
                                <div class='input-group'>
                                    <input type='range' id='ads-sensitivity' min='0.1' max='10' step='0.1' value='1.0' oninput='onAdsSensitivityChange()'>
                                    <input type='number' id='ads-sensitivity-input' min='0.1' max='10' step='0.1' value='1.0' onchange='onAdsSensitivityInputChange()'>
                                </div>
                            </div>
                            <div class='form-row'>
                                <label class='tooltip' data-tooltip='Your in-game field of view (60-120 degrees)'>FOV:</label>
                                <div class='input-group'>
                                    <input type='range' id='fov' min='60' max='120' value='90' oninput='onFovChange()'>
                                    <input type='number' id='fov-input' min='60' max='120' value='90' onchange='onFovInputChange()'>
                                </div>
                            </div>
                        </div>

                        <div class='section-card'>
                            <h3>Configuration</h3>
                            <div class='form-row'>
                                <label>Config Name:</label>
                                <div class='input-group'>
                                    <input type='text' id='config-name' placeholder='Enter config name' value='Default'>
                                    <button onclick='saveConfig()' class='btn btn-primary'>Save</button>
                                </div>
                            </div>
                            <div class='form-row'>
                                <label>Load Config:</label>
                                <div class='input-group'>
                                    <select id='config-list' onchange='onConfigSelect()'>
                                        <option value=''>Select config...</option>
                                    </select>
                                    <button onclick='loadConfig()' class='btn btn-secondary'>Load</button>
                                    <button onclick='deleteConfig()' class='btn btn-danger'>Delete</button>
                                </div>
                            </div>
                            <div class='form-row'>
                                <label>
                                    <input type='checkbox' id='auto-load-config' onchange='onAutoLoadConfigChange()'>
                                    Auto-load on startup
                                </label>
                            </div>
                        </div>

                        <div class='section-card'>
                            <h3>Randomization</h3>
                            <div class='form-row'>
                                <label class='tooltip' data-tooltip='Randomize horizontal recoil movement (0-100%)'>Horizontal Axis:</label>
                                <div class='input-group'>
                                    <input type='range' id='horizontal-randomization' min='0' max='100' value='100' oninput='onHorizontalRandomizationChange()'>
                                    <input type='number' id='horizontal-randomization-input' min='0' max='100' value='100' onchange='onHorizontalRandomizationInputChange()'>
                                </div>
                            </div>
                            <div class='form-row'>
                                <label class='tooltip' data-tooltip='Randomize vertical recoil movement (0-100%)'>Vertical Axis:</label>
                                <div class='input-group'>
                                    <input type='range' id='vertical-randomization' min='0' max='100' value='100' oninput='onVerticalRandomizationChange()'>
                                    <input type='number' id='vertical-randomization-input' min='0' max='100' value='100' onchange='onVerticalRandomizationInputChange()'>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>)";
}

std::string MainWindowStructure::getFeaturesTab()
{
    return R"(
                <div id='features-tab' class='tab-content'>
                    <div class='section-grid'>
                        <div class='section-card'>
                            <h3>Game Features</h3>
                            <div class='form-row'>
                                <div class='checkbox-container'>
                                    <input type='checkbox' id='cursor-check' checked onchange='onCursorCheckChange()'>
                                    <label for='cursor-check'>Cursor Check</label>
                                </div>
                            </div>
                            <div class='form-row'>
                                <label>Recoil Smoothness: <span id='smoothness-value'>50%</span></label>
                                <input type='range' id='recoil-smoothness' min='0' max='100' step='1' value='50' oninput='onSmoothnessChange()'>
                            </div>
                            <div class='form-row'>
                                <div class='checkbox-container'>
                                    <input type='checkbox' id='keybind-sounds' onchange='onKeybindSoundsChange()'>
                                    <label for='keybind-sounds'>Play sound on keybind activation</label>
                                </div>
                            </div>
                        </div>

                        <div class='section-card'>
                            <h3>Auto Code Lock</h3>
                            <div class='form-row'>
                                <div class='checkbox-container'>
                                    <input type='checkbox' id='auto-code-lock' onchange='onAutoCodeLockChange()'>
                                    <label for='auto-code-lock'>Enable Auto Code Lock</label>
                                </div>
                            </div>
                            <div class='form-row'>
                                <label>4-Digit Code:</label>
                                <input type='text' id='code-lock-digits' maxlength='4' pattern='[0-9]{4}' placeholder='1234' value='1234' onchange='onCodeLockDigitsChange()'>
                            </div>
                            <div class='keybind-row'>
                                <label>Auto Code Lock Key:</label>
                                <div class='keybind-input'>
                                    <input type='text' id='auto-code-lock-key' value='F6' readonly onclick='captureKey(this, "autoCodeLock")'>
                                    <button onclick='clearKey("auto-code-lock-key")' class='clear-btn'>Clear</button>
                                </div>
                            </div>
                        </div>

                        <div class='section-card'>
                            <h3>Additional Features</h3>
                            <div class='form-row'>
                                <div class='checkbox-container'>
                                    <input type='checkbox' id='rapid-fire' onchange='onRapidFireChange()'>
                                    <label for='rapid-fire'>Rapid Fire</label>
                                </div>
                            </div>
                            <div class='form-row'>
                                <div class='checkbox-container'>
                                    <input type='checkbox' id='anti-afk' onchange='onAntiAfkChange()'>
                                    <label for='anti-afk'>Anti AFK</label>
                                </div>
                            </div>
                            <div class='form-row'>
                                <label>AFK Interval: <span id='afk-interval-value'>60s</span></label>
                                <input type='range' id='afk-interval' min='30' max='300' step='10' value='60' oninput='onAfkIntervalChange()'>
                            </div>
                        </div>
                    </div>
                </div>)";
}



std::string MainWindowStructure::getKeybindsTab()
{
    return R"(
                <div id='keybinds-tab' class='tab-content'>
                    <div class='section-grid'>
                        <div class='section-card'>
                            <h3>Custom Weapons</h3>
                            <div class='weapon-keybind-row'>
                                <select id='weapon-keybind-select' onchange='onWeaponKeybindSelect()' class='weapon-keybind-dropdown'>
                                    <option value='ak'>AK</option>
                                    <option value='lr300'>LR-300</option>
                                    <option value='mp5'>MP5</option>
                                    <option value='thompson'>Thompson</option>
                                    <option value='m249'>M249</option>
                                    <option value='smg'>Custom SMG</option>
                                </select>
                                <input type='text' id='weapon-keybind-input' value='1' readonly onclick='captureWeaponKeybind()' class='weapon-keybind-input' placeholder='Click to set key'>
                            </div>
                        </div>

                        <div class='section-card'>
                            <h3>Recoil Control Keybinds</h3>
                            <div class='keybind-row'>
                                <label>Toggle Recoil Control:</label>
                                <div class='keybind-input'>
                                    <input type='text' id='toggle-recoil-key' value='F1' readonly onclick='captureKey(this, "toggleRecoil")'>
                                    <button onclick='clearKey("toggle-recoil-key")' class='clear-btn'>Clear</button>
                                </div>
                            </div>
                            <div class='keybind-row'>
                                <label>Crouch:</label>
                                <div class='keybind-input'>
                                    <input type='text' id='crouch-key' value='CTRL' readonly onclick='captureKey(this, "crouchKey")'>
                                    <button onclick='clearKey("crouch-key")' class='clear-btn'>Clear</button>
                                </div>
                            </div>
                            <div class='keybind-row'>
                                <label>Emergency Stop:</label>
                                <div class='keybind-input'>
                                    <input type='text' id='emergency-stop-key' value='F2' readonly onclick='captureKey(this, "emergencyStop")'>
                                    <button onclick='clearKey("emergency-stop-key")' class='clear-btn'>Clear</button>
                                </div>
                            </div>
                        </div>

                        <div class='section-card'>
                            <h3>Application Keybinds</h3>
                            <div class='keybind-row'>
                                <label>Show/Hide Interface:</label>
                                <div class='keybind-input'>
                                    <input type='text' id='show-hide-interface-key' value='F4' readonly onclick='captureKey(this, "showHideInterface")'>
                                    <button onclick='clearKey("show-hide-interface-key")' class='clear-btn'>Clear</button>
                                </div>
                            </div>
                            <div class='keybind-row'>
                                <label>Auto Code Lock:</label>
                                <div class='keybind-input'>
                                    <input type='text' id='auto-code-lock-key' value='F5' readonly onclick='captureKey(this, "autoCodeLock")'>
                                    <button onclick='clearKey("auto-code-lock-key")' class='clear-btn'>Clear</button>
                                </div>
                            </div>
                        </div>

                        <div class='section-card'>
                            <h3>Keybind Settings</h3>
                            <div class='form-row'>
                                <div class='checkbox-container'>
                                    <input type='checkbox' id='global-keybinds' checked onchange='onGlobalKeybindsChange()'>
                                    <label for='global-keybinds'>Enable global keybinds (work outside game)</label>
                                </div>
                            </div>
                            <div class='form-row'>
                                <button onclick='resetKeybinds()' class='secondary'>Reset to Defaults</button>
                                <button onclick='saveKeybinds()' class='primary'>Save Keybinds</button>
                            </div>
                        </div>
                    </div>
                </div>)";
}

std::string MainWindowStructure::getLoadoutsTab()
{
    return R"(
                <div id='loadouts-tab' class='tab-content'>
                    <div class='section-grid'>
                        <div class='section-card'>
                            <h3>Create Loadout</h3>
                            <div class='form-row'>
                                <label>Loadout Name:</label>
                                <input type='text' id='loadout-name' placeholder='Enter loadout name...'>
                            </div>
                            <div class='form-row'>
                                <label>Weapon:</label>
                                <select id='loadout-weapon'>
                                    <option value=''>Select Weapon...</option>
                                    <!-- Assault Rifles -->
                                    <option value='ak47u'>AK47U</option>
                                    <option value='lr300'>LR-300</option>
                                    <option value='m249'>M249</option>
                                    <option value='semi_auto_rifle'>Semi Auto Rifle</option>
                                    <!-- SMGs -->
                                    <option value='mp5'>MP5</option>
                                    <option value='smg'>Custom SMG</option>
                                    <option value='thompson'>Thompson</option>
                                    <option value='hmlmg'>HMLMG</option>
                                    <!-- Pistols -->
                                    <option value='python'>Python</option>
                                    <option value='pistol_revolver'>Revolver</option>
                                    <option value='pistol_semiauto'>Semi Auto Pistol</option>
                                    <option value='m92'>M92</option>
                                </select>
                            </div>
                            <div class='form-row'>
                                <label>Sight:</label>
                                <select id='loadout-sight'>
                                    <option value='none'>None</option>
                                    <option value='holo'>Holo</option>
                                    <option value='8x'>8x Scope</option>
                                    <option value='16x'>16x Scope</option>
                                    <option value='simple'>Simple Sight</option>
                                </select>
                            </div>
                            <div class='form-row'>
                                <label>Muzzle:</label>
                                <select id='loadout-muzzle'>
                                    <option value='none'>None</option>
                                    <option value='silencer'>Silencer</option>
                                    <option value='muzzle_brake'>Muzzle Brake</option>
                                    <option value='muzzle_boost'>Muzzle Boost</option>
                                </select>
                            </div>
                            <div class='loadout-buttons'>
                                <button onclick='saveNewLoadout()' class='primary'>Save Loadout</button>
                            </div>
                        </div>

                        <div class='section-card'>
                            <h3>Saved Loadouts</h3>
                            <div class='loadout-list' id='saved-loadouts-list'>
                                <!-- Loadouts will be populated dynamically -->
                                <div class='loadout-item'>
                                    <div class='loadout-info'>
                                        <div class='loadout-name'>Example Loadout</div>
                                        <div class='loadout-details'>AK47U + Holo + Silencer</div>
                                    </div>
                                    <div class='loadout-actions'>
                                        <button onclick='loadLoadout(1)' class='primary'>Load</button>
                                        <button onclick='deleteLoadout(1)' class='secondary'>Delete</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>)";
}

std::string MainWindowStructure::getMiscellaneousTab()
{
    return R"(
                <div id='miscellaneous-tab' class='tab-content'>
                    <div class='section-grid'>
                        <div class='section-card'>
                            <h3>Miscellaneous</h3>
                            <p>Additional features and settings will be added here.</p>
                        </div>
                    </div>
                </div>)";
}

// Legacy methods for compatibility
std::string MainWindowStructure::getMainTabContent() { return ""; }
std::string MainWindowStructure::getSettingsTabContent() { return ""; }
std::string MainWindowStructure::getMiscTabContent() { return ""; }
std::string MainWindowStructure::getKeybindsTabContent() { return ""; }
std::string MainWindowStructure::getLoadoutsTabContent() { return ""; }

} // namespace views
} // namespace octane
