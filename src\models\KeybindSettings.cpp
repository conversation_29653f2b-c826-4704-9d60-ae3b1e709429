#include "models/KeybindSettings.h"
#include <iostream>
#include <algorithm>
#include <cctype>

namespace octane {
namespace models {

// Key name to virtual key mapping - matches template and C# implementation
const std::unordered_map<std::string, int> KeybindSettings::s_keyNameToVirtualKey = {
    // Control keys
    {"CTRL", VK_CONTROL}, {"Control", VK_CONTROL}, {"Ctrl", VK_CONTROL},
    {"SHIFT", VK_SHIFT}, {"Shift", VK_SHIFT},
    {"ALT", VK_MENU}, {"Alt", VK_MENU},
    {"SPACE", VK_SPACE}, {"Space", VK_SPACE},
    {"TAB", VK_TAB}, {"Tab", VK_TAB},
    {"ENTER", VK_RETURN}, {"Enter", VK_RETURN},
    {"ESC", VK_ESCAPE}, {"Escape", VK_ESCAPE},
    
    // Movement keys (WASD)
    {"W", 0x57}, {"w", 0x57},
    {"A", 0x41}, {"a", 0x41},
    {"S", 0x53}, {"s", 0x53},
    {"D", 0x44}, {"d", 0x44},
    
    // Other common keys
    {"C", 0x43}, {"c", 0x43},
    {"V", 0x56}, {"v", 0x56},
    {"X", 0x58}, {"x", 0x58},
    {"Z", 0x5A}, {"z", 0x5A},
    
    // Function keys
    {"F1", VK_F1}, {"F2", VK_F2}, {"F3", VK_F3}, {"F4", VK_F4},
    {"F5", VK_F5}, {"F6", VK_F6}, {"F7", VK_F7}, {"F8", VK_F8},
    {"F9", VK_F9}, {"F10", VK_F10}, {"F11", VK_F11}, {"F12", VK_F12}
};

KeybindSettings::KeybindSettings() {
    resetToDefaults();
}

void KeybindSettings::resetToDefaults() {
    // Template defaults from m_global::m_keybinds
    m_crouchKey = VK_CONTROL;      // Template: inline int crouch = VK_CONTROL;
    m_forwardKey = 0x57;           // W key
    m_leftKey = 0x41;              // A key  
    m_backwardKey = 0x53;          // S key
    m_rightKey = 0x44;             // D key
    
    // Set display names
    m_crouchKeyName = "CTRL";
    m_forwardKeyName = "W";
    m_leftKeyName = "A";
    m_backwardKeyName = "S";
    m_rightKeyName = "D";
    
    std::cout << "Keybinds reset to template defaults: Crouch=CTRL, Movement=WASD" << std::endl;
}

void KeybindSettings::setCrouchKey(const std::string& keyName) {
    int virtualKey = stringToVirtualKey(keyName);
    if (virtualKey != 0) {
        m_crouchKey = virtualKey;
        m_crouchKeyName = keyName;
        std::cout << "Crouch key set to: " << keyName << " (VK: " << virtualKey << ")" << std::endl;
    } else {
        std::cout << "Warning: Unknown key name '" << keyName << "', keeping current crouch key" << std::endl;
    }
}

void KeybindSettings::setForwardKey(const std::string& keyName) {
    int virtualKey = stringToVirtualKey(keyName);
    if (virtualKey != 0) {
        m_forwardKey = virtualKey;
        m_forwardKeyName = keyName;
        std::cout << "Forward key set to: " << keyName << std::endl;
    }
}

void KeybindSettings::setLeftKey(const std::string& keyName) {
    int virtualKey = stringToVirtualKey(keyName);
    if (virtualKey != 0) {
        m_leftKey = virtualKey;
        m_leftKeyName = keyName;
        std::cout << "Left key set to: " << keyName << std::endl;
    }
}

void KeybindSettings::setBackwardKey(const std::string& keyName) {
    int virtualKey = stringToVirtualKey(keyName);
    if (virtualKey != 0) {
        m_backwardKey = virtualKey;
        m_backwardKeyName = keyName;
        std::cout << "Backward key set to: " << keyName << std::endl;
    }
}

void KeybindSettings::setRightKey(const std::string& keyName) {
    int virtualKey = stringToVirtualKey(keyName);
    if (virtualKey != 0) {
        m_rightKey = virtualKey;
        m_rightKeyName = keyName;
        std::cout << "Right key set to: " << keyName << std::endl;
    }
}

int KeybindSettings::stringToVirtualKey(const std::string& keyName) const {
    if (keyName.empty()) return 0;
    
    // Try direct lookup first
    auto it = s_keyNameToVirtualKey.find(keyName);
    if (it != s_keyNameToVirtualKey.end()) {
        return it->second;
    }
    
    // Handle single character keys (A-Z, 0-9)
    if (keyName.length() == 1) {
        char c = std::toupper(keyName[0]);
        if (c >= 'A' && c <= 'Z') {
            return static_cast<int>(c);
        }
        if (c >= '0' && c <= '9') {
            return static_cast<int>(c);
        }
    }
    
    // Handle function keys (F1-F24)
    if (keyName.length() >= 2 && keyName[0] == 'F') {
        try {
            int fKeyNum = std::stoi(keyName.substr(1));
            if (fKeyNum >= 1 && fKeyNum <= 24) {
                return VK_F1 + fKeyNum - 1;
            }
        } catch (...) {
            // Invalid F-key number
        }
    }
    
    return 0; // Unknown key
}

std::string KeybindSettings::virtualKeyToString(int virtualKey) const {
    // Reverse lookup
    for (const auto& pair : s_keyNameToVirtualKey) {
        if (pair.second == virtualKey) {
            return pair.first;
        }
    }
    
    // Handle A-Z keys
    if (virtualKey >= 'A' && virtualKey <= 'Z') {
        return std::string(1, static_cast<char>(virtualKey));
    }
    
    // Handle 0-9 keys
    if (virtualKey >= '0' && virtualKey <= '9') {
        return std::string(1, static_cast<char>(virtualKey));
    }
    
    // Handle function keys
    if (virtualKey >= VK_F1 && virtualKey <= VK_F24) {
        return "F" + std::to_string(virtualKey - VK_F1 + 1);
    }
    
    return "Unknown";
}

} // namespace models
} // namespace octane
