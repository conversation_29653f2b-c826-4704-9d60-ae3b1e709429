#pragma once

#include <memory>
#include <string>

namespace octane {

// Forward declarations
namespace services {
    class ConfigurationService;
    class DeviceService;
    class AuthenticationService;
}

namespace security {
    class AdvancedSecurityManager;
}

namespace core {

class Application
{
public:
    explicit Application();
    ~Application();

    bool initialize();
    void shutdown();

    services::ConfigurationService* getConfigurationService() const;
    services::DeviceService* getDeviceService() const;
    services::AuthenticationService* getAuthenticationService() const;
    security::AdvancedSecurityManager* getSecurityManager() const;

    bool isInitialized() const { return m_initialized; }

    static std::string version() { return "2.1.0"; }
    static std::string name() { return "Octane Recoil Controller"; }

private:
    bool initializeServices();
    bool initializeSecurity();
    void cleanup();

private:
    std::unique_ptr<services::ConfigurationService> m_configurationService;
    std::unique_ptr<services::DeviceService> m_deviceService;
    std::unique_ptr<services::AuthenticationService> m_authenticationService;
    std::unique_ptr<security::AdvancedSecurityManager> m_securityManager;

    bool m_initialized;
    bool m_shuttingDown;

    static const std::string APPLICATION_NAME;
    static const std::string APPLICATION_VERSION;
};

} // namespace core
} // namespace octane