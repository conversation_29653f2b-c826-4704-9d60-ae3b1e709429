#pragma once
#include <string>

namespace octane {
namespace views {

/**
 * @brief Contains the CSS styles for the WebView2 interface
 */
class MainWindowStyles {
public:
    /**
     * @brief Get the CSS content for styling the main window
     */
    static std::string getCssContent();

private:
    static std::string getModalStyles();
    static std::string getPortStyles();
    static std::string getKeybindStyles();
    static std::string getLoadoutStyles();

    MainWindowStyles() = delete; // Static class
};

} // namespace views
} // namespace octane
