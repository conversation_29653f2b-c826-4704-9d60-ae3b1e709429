#include "services/SoundService.h"
#include <iostream>

namespace octane {
namespace services {

SoundService::SoundService()
    : m_initialized(false)
    , m_enabled(false)
{
}

SoundService::~SoundService()
{
    cleanup();
}

bool SoundService::initialize()
{
    std::cout << "Initializing SoundService..." << std::endl;
    m_initialized = true;
    return true;
}

void SoundService::cleanup()
{
    if (m_initialized) {
        std::cout << "Cleaning up SoundService..." << std::endl;
        m_initialized = false;
    }
}

void SoundService::playKeybindSound()
{
    if (!m_initialized || !m_enabled) {
        return;
    }

    // Play a simple system beep sound
    // In a more advanced implementation, you could load and play WAV files
    Beep(800, 100); // 800Hz for 100ms
}

void SoundService::setEnabled(bool enabled)
{
    m_enabled = enabled;
    std::cout << "Keybind sounds " << (enabled ? "enabled" : "disabled") << std::endl;
}

bool SoundService::isEnabled() const
{
    return m_enabled;
}

} // namespace services
} // namespace octane
