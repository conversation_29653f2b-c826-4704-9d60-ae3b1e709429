#include "services/SecurityService.h"
#include "config/AppConfig.h"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <cstring>
#include <windows.h>
#include <wininet.h>
#include <tlhelp32.h>
#include <psapi.h>
#include <iphlpapi.h>
#include <lmcons.h>


#pragma comment(lib, "wininet.lib")
#pragma comment(lib, "iphlpapi.lib")
#pragma comment(lib, "psapi.lib")

namespace octane {
namespace services {

// Blacklisted processes that indicate cheating tools or debugging
const std::vector<std::string> SecurityService::BLACKLISTED_PROCESSES = {
    "cheatengine.exe", "ce.exe", "cheat engine.exe",
    "artmoney.exe", "gameguardian.exe", "speedhack.exe",
    "processhacker.exe", "procexp.exe", "procexp64.exe",
    "ida.exe", "ida64.exe", "x32dbg.exe", "x64dbg.exe",
    "ollydbg.exe", "immunity.exe",
    "wireshark.exe", "fiddler.exe", "charles.exe"
};

const std::vector<std::string> SecurityService::DEBUGGER_PROCESSES = {
    "windbg.exe", "cdb.exe", "ntsd.exe", "kd.exe",
    "gdb.exe", "lldb.exe", "ida.exe", "ida64.exe"
};

SecurityService::SecurityService()
    : m_monitoring(false)
    , m_reportingEnabled(true)
    , m_discordWebhook("https://discord.com/api/webhooks/1398506465882411079/49oCxzr04R0v7JwYhpxveww1x3_H6pW7VoDocPa6FYPaykMJJegkNddJjkqh2CsbYbKy")
{
    m_lastCheck = std::chrono::steady_clock::now();
}

SecurityService::~SecurityService()
{
    shutdown();
}

bool SecurityService::initialize()
{
    std::cout << "SecurityService: Initializing security monitoring..." << std::endl;
    
    // Report initialization
    reportSecurityEvent("SECURITY_INIT", "Security monitoring system initialized");
    
    return true;
}

void SecurityService::shutdown()
{
    stopMonitoring();
    
    if (m_monitoringThread && m_monitoringThread->joinable()) {
        m_monitoringThread->join();
    }
    
    reportSecurityEvent("SECURITY_SHUTDOWN", "Security monitoring system shutdown");
}

void SecurityService::startMonitoring()
{
    if (m_monitoring) {
        return;
    }

    // Check if security monitoring should be enabled
    auto& config = config::AppConfig::getInstance();
    if (!config.isSecurityEnabled()) {
        std::cout << "SecurityService: Monitoring disabled (Development mode)" << std::endl;
        return;
    }

    m_monitoring = true;
    m_monitoringThread = std::make_unique<std::thread>(&SecurityService::monitoringLoop, this);

    reportSecurityEvent("MONITORING_START", "Active security monitoring started");
    std::cout << "SecurityService: Active monitoring started" << std::endl;
}

void SecurityService::stopMonitoring()
{
    if (!m_monitoring) {
        return;
    }
    
    m_monitoring = false;
    
    if (m_monitoringThread && m_monitoringThread->joinable()) {
        m_monitoringThread->join();
    }
    
    reportSecurityEvent("MONITORING_STOP", "Active security monitoring stopped");
    std::cout << "SecurityService: Active monitoring stopped" << std::endl;
}

void SecurityService::reportSecurityEvent(const std::string& eventType, const std::string& details)
{
    if (!m_reportingEnabled) {
        return;
    }

    // Check if security is enabled in configuration
    auto& config = config::AppConfig::getInstance();
    if (!config.isSecurityEnabled()) {
        if (config.isDebugLoggingEnabled()) {
            std::cout << "SECURITY EVENT (DISABLED) [" << eventType << "]: " << details << std::endl;
        }
        return;
    }

    std::cout << "SECURITY EVENT [" << eventType << "]: " << details << std::endl;

    // Send to Discord webhook only in production mode
    if (config.shouldReportToDiscord()) {
        std::string message = formatSecurityMessage(eventType, details);
        sendToDiscord(message);
    }
}

void SecurityService::reportProcessDetection(const std::string& processName)
{
    std::string details = "Suspicious process detected: " + processName;
    reportSecurityEvent("PROCESS_DETECTION", details);
}

void SecurityService::reportDebuggerDetection()
{
    reportSecurityEvent("DEBUGGER_DETECTION", "Debugger or analysis tool detected");
}

void SecurityService::reportMemoryTampering()
{
    reportSecurityEvent("MEMORY_TAMPERING", "Memory tampering or injection detected");
}

void SecurityService::reportUnauthorizedAccess(const std::string& resource)
{
    std::string details = "Unauthorized access attempt to: " + resource;
    reportSecurityEvent("UNAUTHORIZED_ACCESS", details);
}

void SecurityService::reportSuspiciousActivity(const std::string& activity)
{
    std::string details = "Suspicious activity detected: " + activity;
    reportSecurityEvent("SUSPICIOUS_ACTIVITY", details);
}

void SecurityService::setDiscordWebhook(const std::string& webhookUrl)
{
    m_discordWebhook = webhookUrl;
    std::cout << "SecurityService: Discord webhook configured" << std::endl;
}

std::string SecurityService::formatSecurityMessage(const std::string& eventType, const std::string& details)
{
    // Get current timestamp
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::stringstream ss;
    ss << "🚨 **SECURITY ALERT** 🚨\n";
    ss << "**Event Type:** " << eventType << "\n";
    ss << "**Details:** " << details << "\n";
    char timeBuffer[100];
    ctime_s(timeBuffer, sizeof(timeBuffer), &time_t);
    ss << "**Timestamp:** " << timeBuffer;
    ss << "**System:** " << getSystemInfo() << "\n";
    
    return ss.str();
}

void SecurityService::sendToDiscord(const std::string& message)
{
    if (m_discordWebhook.empty()) {
        return;
    }
    
    // Create JSON payload for Discord webhook
    std::stringstream jsonPayload;
    jsonPayload << "{\"content\":\"" << message << "\"}";
    
    // Send HTTP POST request
    sendHttpRequest(m_discordWebhook, jsonPayload.str());
}

bool SecurityService::sendHttpRequest(const std::string& url, const std::string& data)
{
    HINTERNET hInternet = InternetOpenA("SecurityService/1.0", INTERNET_OPEN_TYPE_DIRECT, NULL, NULL, 0);
    if (!hInternet) {
        return false;
    }

    // Parse URL to extract server and path
    std::string server, path;
    size_t protocolEnd = url.find("://");
    if (protocolEnd != std::string::npos) {
        size_t serverStart = protocolEnd + 3;
        size_t pathStart = url.find("/", serverStart);
        if (pathStart != std::string::npos) {
            server = url.substr(serverStart, pathStart - serverStart);
            path = url.substr(pathStart);
        } else {
            server = url.substr(serverStart);
            path = "/";
        }
    }

    HINTERNET hConnect = InternetConnectA(hInternet, server.c_str(), INTERNET_DEFAULT_HTTPS_PORT, NULL, NULL, INTERNET_SERVICE_HTTP, 0, 0);
    if (!hConnect) {
        InternetCloseHandle(hInternet);
        return false;
    }

    HINTERNET hRequest = HttpOpenRequestA(hConnect, "POST", path.c_str(), NULL, NULL, NULL, INTERNET_FLAG_SECURE, 0);
    if (!hRequest) {
        InternetCloseHandle(hConnect);
        InternetCloseHandle(hInternet);
        return false;
    }

    // Set headers
    const char* headers = "Content-Type: application/json\r\n";

    // Send request
    BOOL result = HttpSendRequestA(hRequest, headers, strlen(headers), (LPVOID)data.c_str(), data.length());

    InternetCloseHandle(hRequest);
    InternetCloseHandle(hConnect);
    InternetCloseHandle(hInternet);

    return result == TRUE;
}

std::string SecurityService::getSystemInfo()
{
    std::stringstream info;
    
    // Get computer name
    char computerName[MAX_COMPUTERNAME_LENGTH + 1];
    DWORD size = sizeof(computerName);
    if (GetComputerNameA(computerName, &size)) {
        info << "PC: " << computerName << " | ";
    }
    
    // Get username
    char username[UNLEN + 1];
    size = sizeof(username);
    if (GetUserNameA(username, &size)) {
        info << "User: " << username;
    }
    
    return info.str();
}

void SecurityService::monitoringLoop()
{
    while (m_monitoring) {
        try {
            checkForSuspiciousProcesses();
            checkForDebuggers();
            checkMemoryIntegrity();
            checkSystemIntegrity();
            
            m_lastCheck = std::chrono::steady_clock::now();
            
            // Sleep for monitoring interval
            std::this_thread::sleep_for(std::chrono::milliseconds(MONITORING_INTERVAL_MS));
        }
        catch (const std::exception& e) {
            std::cerr << "SecurityService monitoring error: " << e.what() << std::endl;
        }
    }
}

void SecurityService::checkForSuspiciousProcesses()
{
    HANDLE hProcessSnap = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hProcessSnap == INVALID_HANDLE_VALUE) {
        return;
    }
    
    PROCESSENTRY32 pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32);
    
    if (Process32First(hProcessSnap, &pe32)) {
        do {
            // Convert WCHAR to std::string
            char processNameBuffer[MAX_PATH];
            WideCharToMultiByte(CP_UTF8, 0, pe32.szExeFile, -1, processNameBuffer, MAX_PATH, NULL, NULL);
            std::string processName = processNameBuffer;
            std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

            // Check against blacklisted processes
            for (const auto& blacklisted : BLACKLISTED_PROCESSES) {
                if (processName.find(blacklisted) != std::string::npos) {
                    reportProcessDetection(processName);
                    break;
                }
            }
        } while (Process32Next(hProcessSnap, &pe32));
    }
    
    CloseHandle(hProcessSnap);
}

void SecurityService::checkForDebuggers()
{
    // Check if debugger is present
    if (IsDebuggerPresent()) {
        reportDebuggerDetection();
    }
    
    // Check for remote debugger
    BOOL remoteDebuggerPresent = FALSE;
    if (CheckRemoteDebuggerPresent(GetCurrentProcess(), &remoteDebuggerPresent) && remoteDebuggerPresent) {
        reportDebuggerDetection();
    }
}

void SecurityService::checkMemoryIntegrity()
{
    // Basic memory integrity check
    // This is a placeholder for more advanced memory protection
}

void SecurityService::checkSystemIntegrity()
{
    // Basic system integrity check
    // This is a placeholder for more advanced system monitoring
}

std::string SecurityService::getProcessList()
{
    // Implementation for getting process list
    return "Process list monitoring";
}

std::string SecurityService::getNetworkInfo()
{
    // Implementation for getting network information
    return "Network monitoring";
}

} // namespace services
} // namespace octane
