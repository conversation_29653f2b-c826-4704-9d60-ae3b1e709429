#include "weapon_database.h"

WeaponDatabase weaponDB;

WeaponDatabase::WeaponDatabase() : weaponCount(0) {
}

void WeaponDatabase::initialize() {
    weaponCount = 0;
    
    // AK47U (ID: 1) - Primary assault rifle
    const float ak47u_pattern[][2] = {
        {0.0f, -2.25779f}, {-0.564448f, -2.25779f}, {-1.69334f, -2.25779f}, {-3.38669f, -2.25779f},
        {-5.64448f, -2.25779f}, {-8.46673f, -2.25779f}, {-11.8534f, -2.25779f}, {-15.8045f, -2.25779f},
        {-20.3201f, -2.25779f}, {-25.4001f, -2.25779f}, {-31.0445f, -2.25779f}, {-37.2534f, -2.25779f},
        {-44.0267f, -2.25779f}, {-51.3645f, -2.25779f}, {-59.2667f, -2.25779f}, {-67.7334f, -2.25779f},
        {-76.7645f, -2.25779f}, {-86.3601f, -2.25779f}, {-96.5201f, -2.25779f}, {-107.244f, -2.25779f},
        {-118.533f, -2.25779f}, {-130.386f, -2.25779f}, {-142.803f, -2.25779f}, {-155.784f, -2.25779f},
        {-169.329f, -2.25779f}, {-183.438f, -2.25779f}, {-198.111f, -2.25779f}, {-213.348f, -2.25779f},
        {-229.149f, -2.25779f}, {-245.514f, -2.25779f}
    };
    addWeapon(1, "AK47U", 133, ak47u_pattern, 30);
    
    // LR300 (ID: 2) - Assault rifle
    const float lr300_pattern[][2] = {
        {0.0f, -1.8f}, {-0.45f, -1.8f}, {-1.35f, -1.8f}, {-2.7f, -1.8f},
        {-4.5f, -1.8f}, {-6.75f, -1.8f}, {-9.45f, -1.8f}, {-12.6f, -1.8f},
        {-16.2f, -1.8f}, {-20.25f, -1.8f}, {-24.75f, -1.8f}, {-29.7f, -1.8f},
        {-35.1f, -1.8f}, {-40.95f, -1.8f}, {-47.25f, -1.8f}, {-54.0f, -1.8f},
        {-61.2f, -1.8f}, {-68.85f, -1.8f}, {-76.95f, -1.8f}, {-85.5f, -1.8f},
        {-94.5f, -1.8f}, {-103.95f, -1.8f}, {-113.85f, -1.8f}, {-124.2f, -1.8f},
        {-135.0f, -1.8f}, {-146.25f, -1.8f}, {-157.95f, -1.8f}, {-170.1f, -1.8f},
        {-182.7f, -1.8f}, {-195.75f, -1.8f}
    };
    addWeapon(2, "LR300", 120, lr300_pattern, 30);
    
    // M249 (ID: 3) - Light machine gun
    const float m249_pattern[][2] = {
        {0.0f, -3.0f}, {-0.75f, -3.0f}, {-2.25f, -3.0f}, {-4.5f, -3.0f},
        {-7.5f, -3.0f}, {-11.25f, -3.0f}, {-15.75f, -3.0f}, {-21.0f, -3.0f},
        {-27.0f, -3.0f}, {-33.75f, -3.0f}, {-41.25f, -3.0f}, {-49.5f, -3.0f},
        {-58.5f, -3.0f}, {-68.25f, -3.0f}, {-78.75f, -3.0f}, {-90.0f, -3.0f},
        {-102.0f, -3.0f}, {-114.75f, -3.0f}, {-128.25f, -3.0f}, {-142.5f, -3.0f},
        {-157.5f, -3.0f}, {-173.25f, -3.0f}, {-189.75f, -3.0f}, {-207.0f, -3.0f},
        {-225.0f, -3.0f}, {-243.75f, -3.0f}, {-263.25f, -3.0f}, {-283.5f, -3.0f},
        {-304.5f, -3.0f}, {-326.25f, -3.0f}
    };
    addWeapon(3, "M249", 100, m249_pattern, 30);
    
    // MP5A4 (ID: 4) - SMG
    const float mp5a4_pattern[][2] = {
        {0.0f, -1.5f}, {-0.375f, -1.5f}, {-1.125f, -1.5f}, {-2.25f, -1.5f},
        {-3.75f, -1.5f}, {-5.625f, -1.5f}, {-7.875f, -1.5f}, {-10.5f, -1.5f},
        {-13.5f, -1.5f}, {-16.875f, -1.5f}, {-20.625f, -1.5f}, {-24.75f, -1.5f},
        {-29.25f, -1.5f}, {-34.125f, -1.5f}, {-39.375f, -1.5f}, {-45.0f, -1.5f},
        {-51.0f, -1.5f}, {-57.375f, -1.5f}, {-64.125f, -1.5f}, {-71.25f, -1.5f},
        {-78.75f, -1.5f}, {-86.625f, -1.5f}, {-94.875f, -1.5f}, {-103.5f, -1.5f},
        {-112.5f, -1.5f}, {-121.875f, -1.5f}, {-131.625f, -1.5f}, {-141.75f, -1.5f},
        {-152.25f, -1.5f}, {-163.125f, -1.5f}
    };
    addWeapon(4, "MP5A4", 90, mp5a4_pattern, 30);
    
    // Thompson (ID: 5) - SMG
    const float thompson_pattern[][2] = {
        {0.0f, -2.1f}, {-0.525f, -2.1f}, {-1.575f, -2.1f}, {-3.15f, -2.1f},
        {-5.25f, -2.1f}, {-7.875f, -2.1f}, {-11.025f, -2.1f}, {-14.7f, -2.1f},
        {-18.9f, -2.1f}, {-23.625f, -2.1f}, {-28.875f, -2.1f}, {-34.65f, -2.1f},
        {-40.95f, -2.1f}, {-47.775f, -2.1f}, {-55.125f, -2.1f}, {-63.0f, -2.1f},
        {-71.4f, -2.1f}, {-80.325f, -2.1f}, {-89.775f, -2.1f}, {-99.75f, -2.1f},
        {-110.25f, -2.1f}, {-121.275f, -2.1f}, {-132.825f, -2.1f}, {-144.9f, -2.1f},
        {-157.5f, -2.1f}, {-170.625f, -2.1f}, {-184.275f, -2.1f}, {-198.45f, -2.1f},
        {-213.15f, -2.1f}, {-228.375f, -2.1f}
    };
    addWeapon(5, "Thompson", 110, thompson_pattern, 30);
}

void WeaponDatabase::addWeapon(int id, const String& name, int delay, const float recoilData[][2], int bulletCount) {
    if (weaponCount >= MAX_WEAPONS || bulletCount > MAX_RECOIL_POINTS) {
        return;
    }
    
    WeaponData& weapon = weapons[weaponCount];
    weapon.weaponId = id;
    weapon.name = name;
    weapon.bulletDelay = delay;
    weapon.maxBullets = bulletCount;
    
    for (int i = 0; i < bulletCount; i++) {
        weapon.pattern[i].x = recoilData[i][0];
        weapon.pattern[i].y = recoilData[i][1];
    }
    
    weaponCount++;
}

const WeaponData* WeaponDatabase::getWeapon(int weaponId) const {
    for (int i = 0; i < weaponCount; i++) {
        if (weapons[i].weaponId == weaponId) {
            return &weapons[i];
        }
    }
    return nullptr;
}

const WeaponData* WeaponDatabase::getWeapon(const String& weaponName) const {
    for (int i = 0; i < weaponCount; i++) {
        if (weapons[i].name == weaponName) {
            return &weapons[i];
        }
    }
    return nullptr;
}

bool WeaponDatabase::hasWeapon(int weaponId) const {
    return getWeapon(weaponId) != nullptr;
}

int WeaponDatabase::getWeaponCount() const {
    return weaponCount;
}

void WeaponDatabase::printWeapons() const {
    Serial.println("=== WEAPON DATABASE ===");
    for (int i = 0; i < weaponCount; i++) {
        const WeaponData& weapon = weapons[i];
        Serial.printf("ID: %d, Name: %s, Delay: %dms, Bullets: %d\n", 
                     weapon.weaponId, weapon.name.c_str(), weapon.bulletDelay, weapon.maxBullets);
    }
    Serial.println("=====================");
}
