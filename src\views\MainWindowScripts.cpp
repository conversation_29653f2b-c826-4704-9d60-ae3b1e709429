#include "views/MainWindowScripts.h"

namespace octane {
namespace views {

std::string MainWindowScripts::getJavaScriptContent()
{
    // This function is now empty - all UI functions moved to MainWindowHtml.cpp
    // to prevent duplicate variable declarations and function conflicts
    return "";
}

std::string MainWindowScripts::getCalibrationScripts()
{
    return R"(
<script>
    // Calibration system removed - focusing on smoothing functionality
</script>
)";
}

} // namespace views
} // namespace octane
