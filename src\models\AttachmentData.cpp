#include "models/AttachmentData.h"
#include <algorithm>

namespace octane {
namespace models {

// Static attachment instances - Scopes
const Attachment AttachmentData::HOLO_SIGHT = AttachmentData::createHoloSight();
const Attachment AttachmentData::SIMPLE_SIGHT = AttachmentData::createSimpleSight();
const Attachment AttachmentData::SMALL_SCOPE = AttachmentData::createSmallScope();
const Attachment AttachmentData::SCOPE_8X = AttachmentData::create8xScope();

// Muzzle attachments
const Attachment AttachmentData::SILENCER = AttachmentData::createSilencer();
const Attachment AttachmentData::MUZZLE_BRAKE = AttachmentData::createMuzzleBrake();
const Attachment AttachmentData::MUZZLE_BOOST = AttachmentData::createMuzzleBoost();

// Other attachments
const Attachment AttachmentData::FLASHLIGHT = AttachmentData::createFlashlight();
const Attachment AttachmentData::LASER_SIGHT = AttachmentData::createLaserSight();
const Attachment AttachmentData::EXTENDED_MAGS = AttachmentData::createExtendedMags();

std::vector<Attachment> AttachmentData::getAllAttachments() {
    return {
        // Scopes
        HOLO_SIGHT, SIMPLE_SIGHT, SMALL_SCOPE, SCOPE_8X,
        // Muzzle attachments
        SILENCER, MUZZLE_BRAKE, MUZZLE_BOOST,
        // Other attachments
        FLASHLIGHT, LASER_SIGHT, EXTENDED_MAGS
    };
}

Attachment AttachmentData::getAttachmentById(int id) {
    auto attachments = getAllAttachments();
    auto it = std::find_if(attachments.begin(), attachments.end(), 
                          [id](const Attachment& a) { return a.getId() == id; });
    return (it != attachments.end()) ? *it : Attachment();
}

Attachment AttachmentData::getAttachmentByName(const std::string& name) {
    auto attachments = getAllAttachments();
    auto it = std::find_if(attachments.begin(), attachments.end(), 
                          [&name](const Attachment& a) { 
                              std::string attachmentName = a.getName();
                              std::string searchName = name;
                              std::transform(attachmentName.begin(), attachmentName.end(), attachmentName.begin(), ::tolower);
                              std::transform(searchName.begin(), searchName.end(), searchName.begin(), ::tolower);
                              return attachmentName == searchName; 
                          });
    return (it != attachments.end()) ? *it : Attachment();
}

std::vector<std::string> AttachmentData::getAttachmentNames() {
    auto attachments = getAllAttachments();
    std::vector<std::string> names;
    names.reserve(attachments.size());
    for (const auto& attachment : attachments) {
        names.push_back(attachment.getName());
    }
    return names;
}

std::vector<Attachment> AttachmentData::getScopeAttachments() {
    return {HOLO_SIGHT, SIMPLE_SIGHT, SMALL_SCOPE, SCOPE_8X};
}

std::vector<Attachment> AttachmentData::getMuzzleAttachments() {
    return {SILENCER, MUZZLE_BRAKE, MUZZLE_BOOST};
}

std::vector<Attachment> AttachmentData::getOtherAttachments() {
    return {FLASHLIGHT, LASER_SIGHT, EXTENDED_MAGS};
}

// Scope attachment implementations
Attachment AttachmentData::createHoloSight() {
    // Holo sight data from Rust dump
    Attachment::Modifier recoilMod(false, 0.9800000190734863f, 0.0f);
    Attachment::Modifier repeatDelayMod(false, 1.0499999523162842f, 0.0f);
    
    return Attachment("Holo Sight", 1, recoilMod, repeatDelayMod, 1.0f, -24.0f, 2.0f);
}

Attachment AttachmentData::createSimpleSight() {
    // Simple sight data from Rust dump
    Attachment::Modifier recoilMod(false, 0.9800000190734863f, 0.0f);
    Attachment::Modifier repeatDelayMod(false, 1.0499999523162842f, 0.0f);
    
    return Attachment("Simple Sight", 2, recoilMod, repeatDelayMod, 0.5f, -5.0f, 1.0f);
}

Attachment AttachmentData::createSmallScope() {
    // Small scope (4x) data from Rust dump
    Attachment::Modifier recoilMod(false, 0.800000011920929f, 0.0f);
    Attachment::Modifier repeatDelayMod(false, 1.0499999523162842f, 0.0f);
    
    return Attachment("4x Scope", 3, recoilMod, repeatDelayMod, 1.0f, -65.0f, 8.0f);
}

Attachment AttachmentData::create8xScope() {
    // 8x scope data from Rust dump
    Attachment::Modifier recoilMod(false, 0.9800000190734863f, 0.0f);
    Attachment::Modifier repeatDelayMod(false, 1.0499999523162842f, 0.0f);
    
    return Attachment("8x Scope", 4, recoilMod, repeatDelayMod, 1.0f, -72.0f, 16.0f);
}

// Muzzle attachment implementations
Attachment AttachmentData::createSilencer() {
    // Silencer data from Rust dump
    Attachment::Modifier recoilMod(true, 0.800000011920929f, 0.0f);
    Attachment::Modifier repeatDelayMod(false, 1.0499999523162842f, 0.0f);
    
    return Attachment("Silencer", 5, recoilMod, repeatDelayMod, 0.0f, 0.0f, 0.0f);
}

Attachment AttachmentData::createMuzzleBrake() {
    // Muzzle brake data from Rust dump
    Attachment::Modifier recoilMod(true, 0.5f, 0.0f);
    Attachment::Modifier repeatDelayMod(false, 1.0499999523162842f, 0.0f);
    
    return Attachment("Muzzle Brake", 6, recoilMod, repeatDelayMod, 0.0f, 0.0f, 0.0f);
}

Attachment AttachmentData::createMuzzleBoost() {
    // Muzzle boost data from Rust dump
    Attachment::Modifier recoilMod(false, 0.9800000190734863f, 0.0f);
    Attachment::Modifier repeatDelayMod(true, 0.8999999761581421f, 0.0f);
    
    return Attachment("Muzzle Boost", 7, recoilMod, repeatDelayMod, 0.0f, 0.0f, 0.0f);
}

// Other attachment implementations
Attachment AttachmentData::createFlashlight() {
    // Flashlight data from Rust dump
    Attachment::Modifier recoilMod(false, 0.9800000190734863f, 0.0f);
    Attachment::Modifier repeatDelayMod(false, 1.0499999523162842f, 0.0f);
    
    return Attachment("Flashlight", 8, recoilMod, repeatDelayMod, 0.0f, 0.0f, 0.0f);
}

Attachment AttachmentData::createLaserSight() {
    // Laser sight data from Rust dump
    Attachment::Modifier recoilMod(false, 0.8500000238418579f, 0.0f);
    Attachment::Modifier repeatDelayMod(false, 1.0499999523162842f, 0.0f);
    
    return Attachment("Laser Sight", 9, recoilMod, repeatDelayMod, 0.0f, 0.0f, 0.0f);
}

Attachment AttachmentData::createExtendedMags() {
    // Extended mags data from Rust dump
    Attachment::Modifier recoilMod(false, 0.9800000190734863f, 0.0f);
    Attachment::Modifier repeatDelayMod(false, 0.8999999761581421f, 0.0f);
    
    return Attachment("Extended Mags", 10, recoilMod, repeatDelayMod, 0.0f, 0.0f, 0.0f);
}

} // namespace models
} // namespace octane
