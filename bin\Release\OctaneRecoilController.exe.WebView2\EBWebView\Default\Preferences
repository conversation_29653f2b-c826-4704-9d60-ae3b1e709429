{"aadc_info": {"age_group": 3}, "accessibility": {"captions": {"headless_caption_enabled": false}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "autocomplete": {"retention_policy_last_version": 138}, "autofill": {"last_version_deduped": 138}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"app_window_placement": {"EdgeDevToolsApp": {"always_on_top": false, "bottom": 740, "left": 100, "maximized": false, "right": 740, "top": 100, "work_area_bottom": 1392, "work_area_left": 0, "work_area_right": 2560, "work_area_top": 0}}, "available_dark_theme_options": "All", "has_seen_welcome_page": false, "recent_theme_color_list": [**********.0, **********.0, **********.0, **********.0, **********.0], "user_level_features_context": {}}, "browser_content_container_height": 0, "browser_content_container_width": 0, "browser_content_container_x": 0, "browser_content_container_y": 0, "collections": {"prism_collections": {"policy": {"cached": 0}}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 18242, "credentials_enable_service": false, "devtools": {"last_open_timestamp": "13398433819344", "preferences": {"EdgeDevToolsLayoutInfo": {"current_dock_state": 0, "horizontal_size": 300, "showEmulationMode": false, "vertical_size": 555}, "closeable-tabs": "{\"security\":true,\"heap-profiler\":true,\"resources\":true,\"lighthouse\":true,\"welcome\":true,\"timeline\":true,\"network\":true,\"cssoverview\":true,\"issues-pane\":true}", "cloud-release-notes": "{\"edgeVersion\":138,\"shouldOpenWelcome\":true,\"help\":[{\"title\":\"DevTools documentation\",\"linkId\":\"2196640\",\"localizedAnnouncementKey\":\"helpCard1\",\"iconName\":\"edge-documentation_book_filled\"},{\"title\":\"Overview of all tools\",\"linkId\":\"2196549\",\"localizedAnnouncementKey\":\"helpCard2\",\"iconName\":\"edge-developer-resources\"},{\"title\":\"Use Copilot to explain Console errors\",\"linkId\":\"2257416\",\"localizedAnnouncementKey\":\"helpCard3\",\"iconName\":\"edge-copilot\"},{\"title\":\"Videos about web development with Microsoft Edge\",\"linkId\":\"2196701\",\"localizedAnnouncementKey\":\"helpCard5\",\"iconName\":\"edge-run_command\"},{\"title\":\"Accessibility testing features\",\"linkId\":\"2196801\",\"localizedAnnouncementKey\":\"helpCard6\",\"iconName\":\"edge-documentation_book_filled\"},{\"title\":\"Use the Console tool to track down problems\",\"linkId\":\"2196702\",\"localizedAnnouncementKey\":\"helpCard7\",\"iconName\":\"edge-console\"},{\"title\":\"Modify and debug JS with the Sources tool\",\"linkId\":\"2196900\",\"localizedAnnouncementKey\":\"helpCard8\",\"iconName\":\"edge-sources\"},{\"title\":\"Find source files for a page using the search tool\",\"linkId\":\"2196802\",\"localizedAnnouncementKey\":\"helpCard9\",\"iconName\":\"edge-sources-search-sources-tab\"},{\"title\":\"Microsoft Edge DevTools for Visual Studio Code\",\"linkId\":\"2196901\",\"localizedAnnouncementKey\":\"helpCard10\",\"iconName\":\"edge-help_tooltips\"}],\"releaseNotes\":[],\"header\":{\"localizedKey\":\"highlightsFromTheLatestMicrosoft\",\"title\":\"What's New\"},\"learnHeader\":{\"localizedKey\":\"learnHeader\",\"title\":\"Learn\"},\"allAnnouncementsLinkText\":{\"localizedKey\":\"allAnnouncementsLinkText\",\"title\":\"View all\"},\"whatsNewVideo\":{\"title\":\"What's New in DevTools 115 - 125\",\"subtitle\":\"Check out our video series on the latest and greatest features in DevTools!\",\"linkId\":\"26zDq9Xhz7k\",\"imageName\":\"whats-new-115-125-thumbnail.jpg\",\"imageAltText\":\"A title card for the Microsoft Edge: What's New in DevTools 115 - 125 video\",\"localizedKey\":\"whatsNewVideo\"},\"viewAllLinkId\":\"2329806\",\"localized\":{\"en-US\":{\"panels/edge_welcome/ReleaseNotes.ts | helpCard1\":{\"message\":\"DevTools documentation\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard2\":{\"message\":\"Overview of all tools\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard3\":{\"message\":\"Use Copilot to explain Console errors\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard5\":{\"message\":\"Videos about web development with Microsoft Edge\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard6\":{\"message\":\"Accessibility testing features\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard7\":{\"message\":\"Use the Console tool to track down problems\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard8\":{\"message\":\"Modify and debug JS with the Sources tool\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard9\":{\"message\":\"Find source files for a page using the search tool\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | helpCard10\":{\"message\":\"Microsoft Edge DevTools for Visual Studio Code\",\"description\":\"Title of a help link in a list of help section.\"},\"panels/edge_welcome/ReleaseNotes.ts | learnHeader\":{\"message\":\"Learn\",\"description\":\"Title text of a header bar in the welcome tool.\"},\"panels/edge_welcome/ReleaseNotes.ts | allAnnouncementsLinkText\":{\"message\":\"View all\",\"description\":\"Title text of a header bar in the welcome tool.\"},\"panels/edge_welcome/ReleaseNotes.ts | highlightsFromTheLatestMicrosoft\":{\"message\":\"What's New\",\"description\":\"Title text of a header bar in the welcome tool.\"},\"panels/edge_welcome/ReleaseNotes.ts | whatsNewVideo\":{\"message\":\"What's New in DevTools 115 - 125\",\"description\":\"Title of a video summarizing the latest release, shown next to a description, above a list of release notes.\"},\"panels/edge_welcome/ReleaseNotes.ts | whatsNewVideoDescription\":{\"message\":\"Check out our video series on the latest and greatest features in DevTools!\",\"description\":\"Description of a video link providing further details\"}}}}", "console.sidebar-selected-filter": "\"message\"", "console.sidebar.width": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "drawer-minimize-state": "false", "edge-inspector.actions-tab-split-view-state": "{\"vertical\":{\"size\":0,\"showMode\":\"Both\"},\"horizontal\":{\"size\":30,\"showMode\":\"Both\"}}", "inspector.drawer-split-view-state": "{\"horizontal\":{\"size\":0,\"showMode\":\"Both\"}}", "inspectorVersion": "38", "last-open-view-in-drawer": "\"console\"", "panel-selected-tab": "\"console\"", "panel-tab-order": "{\"welcome\":10,\"elements\":20,\"sources\":30,\"network\":40,\"timeline\":50,\"heap-profiler\":60,\"resources\":70,\"security\":80,\"lighthouse\":90,\"cssoverview\":100,\"console\":110}", "release-note-version-seen": "138", "sources-panel-navigator-split-view-state": "{\"vertical\":{\"size\":0,\"showMode\":\"Both\"}}", "sources-panel-split-view-state": "{\"vertical\":{\"size\":0,\"showMode\":\"Both\"},\"horizontal\":{\"size\":0,\"showMode\":\"Both\"}}", "tools-used": "{\"welcome\":1753957781521,\"console-view\":1753960219907,\"sources\":1753957781645,\"console\":1753960219998}", "undefined-tab-order": "{\"sources.scope-chain\":10,\"sources.watch\":20}"}, "synced_preferences_sync_disabled": {"syncedInspectorVersion": "38"}}, "domain_diversity": {"last_reporting_timestamp": "13398431366785547"}, "edge": {"bookmarks": {"last_dup_info_record_time": "13398431376697118"}, "msa_sso_info": {"allow_for_non_msa_profile": true}, "profile_sso_info": {"is_msa_first_profile": true, "msa_sso_algo_state": 1}, "services": {"signin_scoped_device_id": "a5bfa9d3-b2e6-4b75-ac16-ab0c18552df7"}}, "edge_rewards": {"cache_data": "CAA=", "coachmark_promotions": {}, "hva_promotions": [], "refresh_status_muted_until": "13399036166658875"}, "edge_ux_config": {"assignmentcontext": "", "dataversion": "0", "experimentvariables": {}, "flights": {}}, "edge_wallet": {"passwords": {"password_lost_report_date": "*****************"}}, "enterprise_profile_guid": "44e28b77-4461-4f88-99b1-98e8825ce1cd", "extension": {"installed_extension_count": 2}, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "commands": {}, "last_chrome_version": "138.0.3351.109", "pdf_upsell_triggered": false, "pinned_extension_migration": true, "pinned_extensions": [], "settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.109\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.109\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "fsd": {"retention_policy_last_version": 138}, "intl": {"selected_languages": "en-GB,en,en-US"}, "media": {"engagement": {"schema_version": 5}}, "muid": {"last_sync": "13398431366685322", "values_seen": []}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "personalization_data_consent": {"personalization_in_context_consent_can_prompt": true, "personalization_in_context_count": 0}, "privacy_sandbox": {"first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 20, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "clear_browsing_data_cookies_exceptions": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "edge_ad_targeting": {}, "edge_ad_targeting_data": {}, "edge_sdsm": {}, "edge_split_screen": {}, "edge_u2f_api_request": {}, "edge_user_agent_token": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "secure_network": {}, "secure_network_sites": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {}, "sleeping_tabs": {}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "tech_scam_detection": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "trackers": {}, "trackers_data": {"https://msedgedevtools.microsoft.com:443,*": {"last_modified": "13398431381769732", "setting": {"count": 1}}}, "tracking_org_exceptions": {}, "tracking_org_relationships": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "138.0.3351.109", "creation_time": "13398431366629438", "edge_password_is_using_new_login_db_path": false, "edge_password_login_db_path_flip_flop_count": 0, "edge_profile_id": "00e06a81-281f-4938-a1bc-c6da72cafe72", "exit_type": "Normal", "has_seen_signin_fre": false, "is_relative_to_aad": true, "last_time_obsolete_http_credentials_removed": 1753957826.738946, "last_time_password_store_metrics_reported": **********.746439, "managed_user_id": "", "name": "Profile 1", "network_pbs": {}, "observed_session_time": {"feedback_rating_in_product_help_observed_session_time_key_138.0.3351.109": 628.0}, "password_hash_data_list": [], "signin_fre_seen_time": "*****************", "were_old_google_logins_removed": true}, "protection": {"macs": {"browser": {"show_home_button": "C35D3399FE36DE8D2601FE461B9FD2DA01D7E349F65A7AEA487A0B8224E5CB75"}, "default_search_provider_data": {"template_url_data": "B795AEA13151714F4EBA4464728EE4855A57A4803AC4A5B0AF0BB17395FD8E01"}, "edge": {"services": {"account_id": "AD26F6DE536ED98037851C306629322AFDB78642229A07A9D636EBB9C6D24AAB", "last_username": "0A46E03F26C3F774B545F72646A8526962E7C4485492DE0E794AA831213BABE5"}}, "enterprise_signin": {"policy_recovery_token": "11577CDF5F7FC003A872F9E2FB5F22487601A33FB93FC6C69457C65F6C536020"}, "extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": "904619343626F3F9626AE6EA635EC4A27BB53D1896CD72AA713809F5D930D50D", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "BEDD762D7E19225BF254B851A1066E9047C32FD4A58F7A2B27FA35F1CC2D5B44"}, "ui": {"developer_mode": "E674A852BEBB40276FB2F64F28E8700F8FA9BD32BB7D4B42A03617C0CCC86ECF"}}, "google": {"services": {"last_signed_in_username": "51DF431D410395A778882DE749540F13A2578D0CB6CE64982B8B23B51D2A38E4"}}, "homepage": "4624EE78FAFA54EFC2F5F0D127E5EF16A686AFE5B19C82B58146659A139652F3", "homepage_is_newtabpage": "612D198AD89A6B60F9B3E67622A3C87F424BDA1392EF1A5E25621553E1EFA013", "media": {"cdm": {"origin_data": "66B9199B925CFCFF0F65A6AC9F0C248472F7CCC226D85DB80198CB41AB09B71D"}, "storage_id_salt": "97134340AA237C7C0A2FD7AA3DBE2F62EE81C8209C116A7E08DD782FA8E21E6C"}, "pinned_tabs": "08BC4A0F10D10D192C181BE3CCA68A8C8DEE9FFF6D321D53F991C64E5C23432D", "prefs": {"preference_reset_time": "09204A6B9D9D3B0288848F335B92324D76B6DB91C2C936FA0F4F91F82A098F0C"}, "safebrowsing": {"incidents_sent": "610DA0CCDA96999F6A8610EE05C94E8AEB99FB1F6900FAF7078F3279DF0EA731"}, "search_provider_overrides": "4CC0C9C9C8AC3EB6EE908AA0259E2678AA340D53BC1591DF68A5AFFF4F709E08", "session": {"restore_on_startup": "475385E2D2FD6F142572BBEB1B640E81E39A0F5FAB0D3FD01631DE2FB8888D3F", "startup_urls": "B35D48FF5BCC785F86DF3969190DDE2CE68881478AD045215068825A5CBB5603"}}}, "reset_prepopulated_engines": false, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "sessions": {"event_log": [{"crashed": false, "time": "13398443689032441", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13398443846416864", "type": 2, "window_count": 0}, {"crashed": false, "time": "13398443864110658", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13398443937931796", "type": 2, "window_count": 0}, {"crashed": false, "time": "13398443942879845", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13398444057767804", "type": 2, "window_count": 0}, {"crashed": false, "time": "13398444119712547", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13398444252725696", "type": 2, "window_count": 0}, {"crashed": false, "time": "13398444353112720", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13398445000779382", "type": 2, "window_count": 0}, {"crashed": false, "time": "13398445019638537", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13398445020854174", "type": 2, "window_count": 0}, {"crashed": false, "time": "13398445024876217", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "13398445068202236", "type": 2, "window_count": 0}, {"crashed": false, "time": "13398445069529997", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "*****************", "type": 2, "window_count": 0}, {"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "*****************", "type": 2, "window_count": 0}, {"crashed": false, "time": "*****************", "type": 0}], "session_data_status": 1}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-GB"], "dictionary": ""}, "sync": {"passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "user_experience_metrics": {"personalization_data_consent_enabled_last_known_value": false}}