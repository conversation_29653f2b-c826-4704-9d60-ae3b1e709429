#ifndef USB_MANAGER_H
#define USB_MANAGER_H

#include <Arduino.h>
#include <USB.h>
#include <USBHIDMouse.h>
#include <USBCDC.h>

// ===== USB MANAGER CLASS =====
class USBManager {
private:
    bool usbReady;
    bool hidReady;
    bool cdcReady;

public:
    USBManager();
    void begin();
    bool isReady() const;
    bool isHidReady() const;
    bool isCdcReady() const;
    void sendMouseMove(int16_t x, int16_t y);
    void sendMouseClick(uint8_t button, bool pressed);
    void sendDebugMessage(const String& message);

private:
    void initializeUSB();
    void waitForUSBReady();
};

// Global USB manager instance
extern USBManager usbManager;

#endif // USB_MANAGER_H
