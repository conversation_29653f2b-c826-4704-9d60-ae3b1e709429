
; ESP32-S2 HID Mouse Firmware - PlatformIO Configuration
; Octane Recoil Control System
; Target: ESP32-S2 Mini (Lolin S2 Mini)

; Made by Lag

[env:lolin_s2_mini]
platform = espressif32
board = lolin_s2_mini
framework = arduino

; Serial configuration
monitor_speed = 115200
upload_speed = 921600

; Upload port (adjust if needed)
upload_port = COM10
upload_protocol = esptool

; Build flags for USB HID and MAXIMUM PERFORMANCE
build_flags =
    -DARDUINO_USB_MODE=1
    -DARDUINO_USB_CDC_ON_BOOT=0
    -DCORE_DEBUG_LEVEL=0        ; Disable debug for maximum speed
    -DBOARD_HAS_PSRAM
    -O3                         ; Maximum optimization
    -DCONFIG_ARDUHAL_LOG_DEFAULT_LEVEL=0  ; Disable Arduino HAL logging
    -DFIRMWARE_VERSION="2.2.0-ULTRA"

; Board configuration
board_build.partitions = default.csv
board_build.flash_mode = dio
board_build.f_cpu = 240000000L
board_build.f_flash = 80000000L
board_build.flash_size = 4MB

; Required libraries
lib_deps =
    bblanchon/Arduino<PERSON>son@^6.21.0

; Debug configuration
debug_tool = esp-prog
debug_init_break = tbreak setup
