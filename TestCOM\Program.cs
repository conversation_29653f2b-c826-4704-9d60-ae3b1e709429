using System;
using System.IO.Ports;
using System.Threading;

class TestCOM
{
    static void Main(string[] args)
    {
        Console.WriteLine("=== ESP32 COM Port Test ===");
        Console.WriteLine();

        // List all available COM ports
        Console.WriteLine("Available COM ports:");
        string[] ports = SerialPort.GetPortNames();
        foreach (string port in ports)
        {
            Console.WriteLine($"  - {port}");
        }
        Console.WriteLine();

        // Test COM10 specifically
        string testPort = "COM10";
        Console.WriteLine($"Testing {testPort}...");

        try
        {
            using (SerialPort serial = new SerialPort(testPort, 115200, Parity.None, 8, StopBits.One))
            {
                serial.ReadTimeout = 2000;
                serial.WriteTimeout = 2000;
                
                Console.WriteLine($"Opening {testPort}...");
                serial.Open();
                Console.WriteLine("✅ Port opened successfully!");

                // Clear buffers
                serial.DiscardInBuffer();
                serial.DiscardOutBuffer();

                // Send test commands
                Console.WriteLine("Sending test commands...");
                
                // Try simple ping
                serial.WriteLine("ping");
                Thread.Sleep(500);
                
                if (serial.BytesToRead > 0)
                {
                    string response = serial.ReadExisting();
                    Console.WriteLine($"Response to 'ping': {response}");
                }

                // Try identify command
                serial.WriteLine("identify");
                Thread.Sleep(500);
                
                if (serial.BytesToRead > 0)
                {
                    string response = serial.ReadExisting();
                    Console.WriteLine($"Response to 'identify': {response}");
                }

                // Try get_version command
                serial.WriteLine("get_version");
                Thread.Sleep(500);
                
                if (serial.BytesToRead > 0)
                {
                    string response = serial.ReadExisting();
                    Console.WriteLine($"Response to 'get_version': {response}");
                }

                // Listen for any spontaneous messages
                Console.WriteLine("Listening for messages (5 seconds)...");
                for (int i = 0; i < 10; i++)
                {
                    Thread.Sleep(500);
                    if (serial.BytesToRead > 0)
                    {
                        string response = serial.ReadExisting();
                        Console.WriteLine($"Received: {response}");
                    }
                }

                Console.WriteLine("✅ Test completed successfully!");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error: {ex.Message}");
        }

        Console.WriteLine();
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
}
