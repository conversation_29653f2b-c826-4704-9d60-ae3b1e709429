#include "services/RecoilControlService.h"
#include "services/ESP32Service.h"
#include "models/WeaponData.h"
#include <iostream>
#define NOMINMAX  // Prevent Windows.h from defining min/max macros
#include <Windows.h>
#include <thread>
#include <chrono>
#include <algorithm>
#include <cmath>
#include <map>
#include <string>
#include <cctype>

// Windows API constants for cursor checking
#ifndef CURSOR_SHOWING
#define CURSOR_SHOWING 0x00000001
#endif

namespace octane {
namespace services {

RecoilControlService::RecoilControlService()
    : m_initialized(false)
    , m_enabled(false)
    , m_running(false)
    , m_isFiring(false)
    , m_currentBullet(0)
    , m_cursorCheckEnabled(true)  // Default to enabled
    , m_autoCodeLockEnabled(false)
    , m_codeLockDigits("1234")
    , m_rapidFireEnabled(false)
    , m_autoClickerEnabled(false)
    , m_antiAfkEnabled(false)
    , m_afkInterval(60)
    , m_compensateHipfire(false)
    , m_hipfireMode("always")
    , m_horizontalRandomization(100)
    , m_verticalRandomization(100)
    , m_humanizationEnabled(false)
    , m_humanizationRandomization(50)
    , m_humanizationLevel(30)
    , m_interfaceVisible(true)
    , m_currentBulletIndex(0)
    , m_recoilActive(false)
    , m_optimizedSequenceSent(false)
{
    // Initialize timing variables
    m_lastAfkTime = std::chrono::steady_clock::now();
    m_lastCodeLockTime = std::chrono::steady_clock::now();
    m_lastRecoilTime = std::chrono::steady_clock::now();
    m_recoilStartTime = std::chrono::steady_clock::now();
}

RecoilControlService::~RecoilControlService()
{
    stopRecoilControl();
    shutdown();
}

bool RecoilControlService::initialize()
{
    if (m_initialized) {
        return true;
    }

    std::cout << "Initializing RecoilControlService..." << std::endl;

    // Initialize with default weapon (AK-47)
    auto weapons = models::WeaponData::getAllWeapons();
    if (!weapons.empty()) {
        m_currentWeapon = weapons[0]; // AK-47
        std::cout << "Default weapon set to: " << m_currentWeapon.getName() << std::endl;
    }

    std::cout << "RecoilControlService using template approach - no smart calibration" << std::endl;

    m_initialized = true;
    std::cout << "RecoilControlService initialized successfully" << std::endl;
    return true;
}

void RecoilControlService::shutdown()
{
    if (!m_initialized) {
        return;
    }

    std::cout << "Shutting down RecoilControlService..." << std::endl;
    m_enabled = false;
    m_initialized = false;
}

void RecoilControlService::setWeapon(const models::Weapon& weapon)
{
    m_currentWeapon = weapon;
    std::cout << "Weapon set to: " << weapon.getName()
              << " (ID: " << weapon.getId()
              << ", Delay: " << weapon.getDelay() << "ms)" << std::endl;
}

void RecoilControlService::setWeaponById(int weaponId)
{
    auto weapon = models::WeaponData::getWeaponById(weaponId);
    if (!weapon.getName().empty()) {
        setWeapon(weapon);
    } else {
        std::cerr << "Weapon with ID " << weaponId << " not found" << std::endl;
    }
}

void RecoilControlService::setWeaponByName(const std::string& weaponName)
{
    auto weapon = models::WeaponData::getWeaponByName(weaponName);
    if (!weapon.getName().empty()) {
        setWeapon(weapon);
    } else {
        std::cerr << "Weapon with name '" << weaponName << "' not found" << std::endl;
    }
}

const models::Weapon& RecoilControlService::getCurrentWeapon() const
{
    return m_currentWeapon;
}

void RecoilControlService::setRecoilSettings(const models::RecoilSettings& settings)
{
    m_recoilSettings = settings;
    std::cout << "Recoil settings updated" << std::endl;
}

const models::RecoilSettings& RecoilControlService::getRecoilSettings() const
{
    return m_recoilSettings;
}

void RecoilControlService::setEnabled(bool enabled)
{
    m_enabled = enabled;
    std::cout << "Recoil control " << (enabled ? "enabled" : "disabled") << std::endl;
}

bool RecoilControlService::isEnabled() const
{
    return m_enabled;
}

void RecoilControlService::startRecoilControl()
{
    if (m_running) {
        return; // Already running
    }

    std::cout << "Starting recoil control loop..." << std::endl;
    m_running = true;
    m_recoilThread = std::make_unique<std::thread>(&RecoilControlService::recoilControlLoop, this);
}

void RecoilControlService::stopRecoilControl()
{
    if (!m_running) {
        return; // Not running
    }

    std::cout << "Stopping recoil control loop..." << std::endl;
    m_running = false;

    if (m_recoilThread && m_recoilThread->joinable()) {
        m_recoilThread->join();
    }
    m_recoilThread.reset();
}

void RecoilControlService::setESP32Service(std::shared_ptr<ESP32Service> esp32Service)
{
    m_esp32Service = esp32Service;
    std::cout << "ESP32 service set for recoil control" << std::endl;
}

// Main recoil control loop (template approach from core.cpp initialize())
void RecoilControlService::recoilControlLoop()
{
    std::cout << "=== Template recoil control loop started ===" << std::endl;

    while (m_running) {
        try {
            // Process keybinds for features
            processKeybinds();

            // Template approach: execute pattern directly in loop like template core.cpp line 50-68
            if (m_enabled && !m_currentWeapon.getName().empty()) {
                executeRecoilPattern();
            }

            // Reasonable sleep to prevent overwhelming ESP32
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
        catch (const std::exception& ex) {
            std::cerr << "Error in recoil control loop: " << ex.what() << std::endl;
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }

    std::cout << "=== Template recoil control loop ended ===" << std::endl;
}

bool RecoilControlService::isMouseButtonPressed(int button) const
{
    return (GetAsyncKeyState(button) & 0x8000) != 0;
}

bool RecoilControlService::areBothMouseButtonsPressed() const
{
    return isMouseButtonPressed(0x01) && isMouseButtonPressed(0x02); // VK_LBUTTON && VK_RBUTTON
}

// Execute recoil pattern with proper timing to prevent ESP32 overload
void RecoilControlService::executeRecoilPattern()
{
    if (m_currentWeapon.getName().empty()) {
        return;
    }

    auto recoilPattern = m_currentWeapon.getRecoilPattern();
    if (recoilPattern.empty()) {
        return;
    }

    // CURSOR CHECK - Skip recoil when cursor is VISIBLE (in menus) - only if enabled
    // Run recoil only when cursor is HIDDEN (not visible)
    if (m_cursorCheckEnabled && isCursorVisible()) {
        // Reset recoil state when cursor is visible
        m_recoilActive = false;
        m_currentBulletIndex = 0;
        return;
    }

    // Check proper mouse button combination based on ADS setting with stability
    bool shouldActivate = false;
    if (m_recoilSettings.isAdsEnabled()) {
        // ADS enabled: only need LEFT mouse button (check high bit for current state)
        shouldActivate = (GetAsyncKeyState(VK_LBUTTON) & 0x8000) != 0;
    } else {
        // ADS disabled: need RIGHT + LEFT mouse buttons
        shouldActivate = ((GetAsyncKeyState(VK_LBUTTON) & 0x8000) != 0) &&
                        ((GetAsyncKeyState(VK_RBUTTON) & 0x8000) != 0);
    }

    // Add button state stability check to prevent bouncing
    static bool lastButtonState = false;
    static auto lastButtonChangeTime = std::chrono::steady_clock::now();
    auto now = std::chrono::steady_clock::now();

    if (shouldActivate != lastButtonState) {
        lastButtonChangeTime = now;
        lastButtonState = shouldActivate;
        // Debug logging for button state changes with timing
        auto currentTime = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
        std::cout << "Button state changed to: " << (shouldActivate ? "PRESSED" : "RELEASED")
                  << " at " << currentTime << "ms" << std::endl;
    }

    // TIMING FIX: Reduce button stability delay for better responsiveness
    // Old: 100ms was too slow and caused missed rapid clicks
    // New: 20ms provides stability while maintaining responsiveness
    auto timeSinceChange = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastButtonChangeTime).count();
    if (timeSinceChange < 20) {
        return; // Button state not stable yet (reduced from 100ms to 20ms)
    }

    // Handle recoil state transitions
    if (!shouldActivate) {
        // Mouse buttons released - reset recoil
        if (m_recoilActive) {
            // Calculate time since spray started
            auto timeSinceSprayStart = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_recoilStartTime).count();

            // TIMING FIX: Reduce minimum spray duration for better responsiveness
            // Old: 300ms was too long and prevented proper burst fire detection
            // New: 100ms allows proper burst detection while preventing accidental resets
            if (timeSinceSprayStart > 100) { // At least 100ms spray duration (reduced from 300ms)
                m_recoilActive = false;
                int endedAtBullet = m_currentBulletIndex;
                m_currentBulletIndex = 0;
                std::cout << "Recoil sequence ended at bullet " << endedAtBullet
                          << " (spray duration: " << timeSinceSprayStart << "ms)" << std::endl;

                // Stop weapon-based recoil on ESP32
                stopWeaponBasedRecoil();
            } else {
                // Only log once to prevent spam
                static auto lastLogTime = std::chrono::steady_clock::now();
                auto timeSinceLastLog = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastLogTime).count();
                if (timeSinceLastLog > 100) { // Log at most once per 100ms
                    std::cout << "Reset blocked - spray too short: " << timeSinceSprayStart << "ms (need 100ms)" << std::endl;
                    lastLogTime = now;
                }
            }
        }
        return;
    }

    // Mouse buttons pressed - start or continue recoil
    if (!m_recoilActive) {
        // Start new recoil sequence
        m_recoilActive = true;
        m_currentBulletIndex = 0;
        m_optimizedSequenceSent = false; // Reset optimized sequence flag
        m_recoilStartTime = std::chrono::steady_clock::now();
        m_lastRecoilTime = m_recoilStartTime;
        std::cout << "Starting new recoil sequence" << std::endl;

        // Start weapon-based recoil on ESP32
        startWeaponBasedRecoil();
    }

    // Check if enough time has passed for next bullet (weapon delay timing)
    // Reuse the 'now' variable from button state checking above
    auto timeSinceLastRecoil = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_lastRecoilTime).count();

    // TIMING FIX: Use actual weapon delay for accurate fire rate matching
    int weaponDelay = m_currentWeapon.getDelay();

    // FIXED TIMING: Use exact weapon delay for perfect synchronization
    // This ensures consistent timing that matches the game's exact fire rate
    int exactDelay = weaponDelay; // Use exact weapon delay (133ms for AK)

    // Reduced logging - only show weapon changes, not every bullet timing
    static std::string lastWeaponName = "";
    if (m_currentWeapon.getName() != lastWeaponName) {
        std::cout << "🎯 Weapon: " << m_currentWeapon.getName() << " (Fixed Delay: " << exactDelay << "ms)" << std::endl;
        lastWeaponName = m_currentWeapon.getName();
    }

    if (timeSinceLastRecoil < exactDelay) {
        return; // Wait for exact weapon timing
    }

    // Check if we've reached the end of the pattern - RESTART THE PATTERN
    if (m_currentBulletIndex >= static_cast<int>(recoilPattern.size()) ||
        m_currentBulletIndex >= static_cast<int>(m_currentWeapon.getMaxBulletCount())) {
        std::cout << "🔄 Restarting recoil pattern (completed " << m_currentBulletIndex << " bullets)" << std::endl;
        m_currentBulletIndex = 0; // Restart pattern from beginning
    }

    if (!m_enabled) {
        return; // Recoil disabled
    }

    // Process current bullet
    if (m_currentBulletIndex < static_cast<int>(recoilPattern.size())) {
        auto recoilVector = recoilPattern[m_currentBulletIndex];

            // TEMPLATE EXACT FORMULA from core.cpp lines 6-7
            float sensitivity = m_recoilSettings.getSensitivity();
            float adsSensitivity = m_recoilSettings.getAdsSensitivity();
            float fov = m_recoilSettings.getFOV();

            // Template formula: input.x = round(input.x / (-0.03 * (sensitivity * ads_sensitivity * 3.0) * (fov / 100)))
            float scale = -0.03f * (sensitivity * adsSensitivity * 3.0f) * (fov / 100.0f);

            float pixelX = std::round(recoilVector.x / scale);
            float pixelY = std::round(recoilVector.y / scale);

            // Apply crouch modifier (template core.cpp line 63: data *= 0.5f)
            // Use the movement system's crouch detection for consistency
            bool isCrouching = this->isCrouching() || this->isCrouchWalking();

            // DEBUG: Log crouch state for troubleshooting
            if (m_currentBulletIndex == 0) { // Only log once per spray
                std::cout << "🔍 Crouch Debug: isCrouching=" << (isCrouching ? "YES" : "NO")
                         << ", CTRL pressed=" << ((GetAsyncKeyState(VK_CONTROL) & 0x8000) ? "YES" : "NO")
                         << ", walking=" << (isWalking() ? "YES" : "NO") << std::endl;
            }

            if (isCrouching) {
                pixelX *= 0.5f;
                pixelY *= 0.5f;
            }

            // CRITICAL FIX: Convert to clean integers before sending
            int intPixelX = static_cast<int>(std::round(pixelX));
            int intPixelY = static_cast<int>(std::round(pixelY));

            // Enhanced logging to track bullet progression
            std::cout << "Bullet " << (m_currentBulletIndex + 1) << ": Raw(" << recoilVector.x << "," << recoilVector.y
                      << ") -> Pixel(" << intPixelX << "," << intPixelY << ") MoveMult:" << (isCrouching ? 0.5f : 1.0f) << std::endl;

            // Apply movement multiplier using template's exact system
            float movementMultiplier = getMovementMultiplier();
            pixelX *= movementMultiplier;
            pixelY *= movementMultiplier;

        // ULTRA-OPTIMIZED: Use sequence-based recoil for maximum performance
        float smoothing = m_recoilSettings.getSmoothing();

        // Use smooth movement approach (sending multiple MOUSE_MOVE commands)
        if (smoothing > 0.0f) {
            // Fallback to individual commands for compatibility
            bool useHardwareInterpolation = (smoothing >= 75.0f); // Use hardware for 75%+ smoothing
            int steps = static_cast<int>(smoothing / 10.0f) + 3; // 3-13 steps based on 0-100% smoothing

            if (useHardwareInterpolation && m_esp32Service && m_esp32Service->isConnected()) {
                // Option 2: Hardware interpolation on ESP32
                std::cout << "🔧 Using Hardware Interpolation (smoothing: " << smoothing << "%)" << std::endl;
                sendHardwareInterpolatedMovement(intPixelX, intPixelY, steps, exactDelay);
            } else {
                // Option 3: Software interpolation (proven method)
                std::cout << "✨ Using Software Interpolation (smoothing: " << smoothing << "%)" << std::endl;
                sendSmoothMouseMovement(intPixelX, intPixelY, steps, exactDelay);
            }
        } else {
            // Use direct movement (old jerky style)
            std::cout << "⚡ Using Direct Movement (smoothing disabled)" << std::endl;
            sendMouseMovement(static_cast<float>(intPixelX), static_cast<float>(intPixelY));
        }

        // Update timing and advance to next bullet
        m_lastRecoilTime = now;
        m_currentBulletIndex++;

        // Log bullet progression (bullet number is 1-based for display)
        std::cout << "Processed bullet " << m_currentBulletIndex << "/" << recoilPattern.size() << std::endl;
    }
}

void RecoilControlService::sendMouseMovement(float deltaX, float deltaY)
{
    if (m_esp32Service && m_esp32Service->isConnected()) {
        try {
            // Send recoil data to ESP32 for hardware mouse control
            bool sent = m_esp32Service->sendRecoilData(deltaX, deltaY);
            // Reduced logging - only log errors, not every successful send
            // if (sent) {
            //     std::cout << "Queued recoil data: " << deltaX << ", " << deltaY << " (ESP32 will accumulate/filter)" << std::endl;
            // }
        }
        catch (const std::exception& ex) {
            std::cerr << "Error sending mouse movement to ESP32: " << ex.what() << std::endl;
        }
    } else {
        std::cout << "ESP32 not connected, recoil data: " << deltaX << ", " << deltaY << std::endl;
    }
}

void RecoilControlService::sendSmoothMouseMovement(int deltaX, int deltaY, int steps, int totalTimeMs)
{
    // Simple smooth movement - break large movements into smaller steps
    if (steps <= 0) steps = 8;
    if (totalTimeMs <= 0) totalTimeMs = 133;

    int stepDelay = totalTimeMs / steps;
    if (stepDelay < 1) stepDelay = 1;

    for (int step = 1; step <= steps; ++step) {
        int step_x = (deltaX * step) / steps - (deltaX * (step - 1)) / steps;
        int step_y = (deltaY * step) / steps - (deltaY * (step - 1)) / steps;

        if (step_x != 0 || step_y != 0) {
            sendMouseMovement(static_cast<float>(step_x), static_cast<float>(step_y));
        }

        if (step < steps) {
            accurateSleep(stepDelay);
        }
    }
}

void RecoilControlService::sendHardwareInterpolatedMovement(int deltaX, int deltaY, int steps, int totalTimeMs)
{
    // HARDWARE INTERPOLATION - Uses ESP32's built-in RECOIL_INTERPOLATE command
    // This offloads the interpolation work to the ESP32 firmware for maximum performance

    if (steps <= 0) steps = 8; // Default to 8 steps
    if (totalTimeMs <= 0) totalTimeMs = 133; // Default weapon delay

    std::cout << "🔧 Hardware interpolation: (" << deltaX << "," << deltaY << ") in " << steps << " steps" << std::endl;

    // Track current position for smooth transitions between recoil points
    static int currentX = 0;
    static int currentY = 0;

    // Calculate target position
    int targetX = currentX + deltaX;
    int targetY = currentY + deltaY;

    // Send RECOIL_INTERPOLATE command to ESP32
    // Format: RECOIL_INTERPOLATE fromX,fromY,toX,toY,steps,totalTime
    std::string command = "RECOIL_INTERPOLATE " +
                         std::to_string(currentX) + "," + std::to_string(currentY) + "," +
                         std::to_string(targetX) + "," + std::to_string(targetY) + "," +
                         std::to_string(steps) + "," + std::to_string(totalTimeMs);

    if (m_esp32Service && m_esp32Service->isConnected()) {
        bool success = m_esp32Service->sendCommand(command);
        if (success) {
            std::cout << "  ESP32 Command: " << command << std::endl;
            // Update position tracking
            currentX = targetX;
            currentY = targetY;

            // Wait for the interpolation to complete
            accurateSleep(totalTimeMs);
        } else {
            std::cout << "  ⚠️  Hardware interpolation failed, falling back to software" << std::endl;
            // Fallback to software interpolation
            sendSmoothMouseMovement(deltaX, deltaY, steps, totalTimeMs);
        }
    } else {
        std::cout << "  ⚠️  ESP32 not connected, falling back to software" << std::endl;
        // Fallback to software interpolation
        sendSmoothMouseMovement(deltaX, deltaY, steps, totalTimeMs);
    }
}

void RecoilControlService::accurateSleep(int milliseconds)
{
    // TEMPLATE ACCURATE SLEEP - Exact copy from template mouse.cpp line 18-35
    if (milliseconds <= 0) return;

    LONGLONG TimerResolution;
    LONGLONG WantedTime;
    LONGLONG CurrentTime;

    QueryPerformanceFrequency((LARGE_INTEGER*)&TimerResolution);
    TimerResolution /= 1000;

    QueryPerformanceCounter((LARGE_INTEGER*)&CurrentTime);
    WantedTime = CurrentTime / TimerResolution + milliseconds;
    CurrentTime = 0;
    while (CurrentTime < WantedTime)
    {
        QueryPerformanceCounter((LARGE_INTEGER*)&CurrentTime);
        CurrentTime /= TimerResolution;
    }
}

void RecoilControlService::setSensitivity(float sensitivity)
{
    m_recoilSettings.setSensitivity(sensitivity);
    std::cout << "Recoil sensitivity updated to: " << sensitivity << std::endl;
}

void RecoilControlService::setAdsSensitivity(float adsSensitivity)
{
    m_recoilSettings.setAdsSensitivity(adsSensitivity);
    std::cout << "ADS sensitivity updated to: " << adsSensitivity << std::endl;
}

void RecoilControlService::setFOV(float fov)
{
    m_recoilSettings.setFOV(fov);
    std::cout << "FOV updated to: " << fov << std::endl;
}

void RecoilControlService::setSmoothness(int smoothness)
{
    // Convert percentage to smoothing value (0-100% -> 0.0-100.0)
    m_recoilSettings.setSmoothing(static_cast<float>(smoothness));
    std::cout << "Recoil smoothness updated to: " << smoothness << "%" << std::endl;
}

void RecoilControlService::setCursorCheckEnabled(bool enabled)
{
    m_cursorCheckEnabled = enabled;
    std::cout << "Cursor check " << (enabled ? "enabled" : "disabled") << std::endl;
}

void RecoilControlService::setAutoCodeLockEnabled(bool enabled)
{
    m_autoCodeLockEnabled = enabled;
    std::cout << "Auto code lock " << (enabled ? "enabled" : "disabled") << std::endl;
}

void RecoilControlService::setCodeLockDigits(const std::string& digits)
{
    m_codeLockDigits = digits;
    std::cout << "Code lock digits set to: " << digits << std::endl;
}

void RecoilControlService::setRapidFireEnabled(bool enabled)
{
    m_rapidFireEnabled = enabled;
    std::cout << "Rapid fire " << (enabled ? "enabled" : "disabled") << std::endl;
}

void RecoilControlService::setAutoClickerEnabled(bool enabled)
{
    m_autoClickerEnabled = enabled;
    std::cout << "Auto clicker " << (enabled ? "enabled" : "disabled") << std::endl;
}

void RecoilControlService::setAntiAfkEnabled(bool enabled)
{
    m_antiAfkEnabled = enabled;
    std::cout << "Anti AFK " << (enabled ? "enabled" : "disabled") << std::endl;
}

void RecoilControlService::setAfkInterval(int seconds)
{
    m_afkInterval = seconds;
    std::cout << "AFK interval set to: " << seconds << " seconds" << std::endl;
}

void RecoilControlService::setKeybind(const std::string& action, const std::string& keyName)
{
    int vkCode = getVirtualKeyCode(keyName);
    if (vkCode != 0) {
        m_keybinds[action] = vkCode;
        m_keyStates[action] = false;
        std::cout << "Keybind set: " << action << " = " << keyName << " (VK: " << vkCode << ")" << std::endl;
    } else {
        std::cerr << "Unknown key name: " << keyName << std::endl;
    }
}

void RecoilControlService::clearKeybind(const std::string& action)
{
    m_keybinds.erase(action);
    m_keyStates.erase(action);
    std::cout << "Keybind cleared: " << action << std::endl;
}

// TEMPLATE MOVEMENT DETECTION SYSTEM - Exact copy from template movement.cpp
bool RecoilControlService::isWalking() const
{
    // Template line 15: hardcoded WASD keys (0x57=W, 0x41=A, 0x53=S, 0x44=D)
    return (GetAsyncKeyState(0x57) || GetAsyncKeyState(0x41) || GetAsyncKeyState(0x53) || GetAsyncKeyState(0x44));
}

bool RecoilControlService::isCrouching() const
{
    // Template line 5: crouch key without walking
    return GetAsyncKeyState(VK_CONTROL) && !isWalking();
}

bool RecoilControlService::isCrouchWalking() const
{
    // Template line 10: crouch key with walking
    return GetAsyncKeyState(VK_CONTROL) && isWalking();
}

float RecoilControlService::getMovementMultiplier() const
{
    // TEMPLATE MOVEMENT MULTIPLIER SYSTEM (movement.cpp line 18-36)
    if (isCrouchWalking()) {
        // Template line 25: for assault rifle (weapon 1) use 1.11f
        return 1.11f;
    }
    else if (isWalking()) {
        // Template line 32: walking multiplier for assault rifle
        return 1.17f;
    }
    else {
        // Template line 35: standing still
        return 1.0f;
    }
}

bool RecoilControlService::isCursorVisible() const
{
    // FIXED CURSOR CHECK - Check if cursor is visible (in menus)
    // Recoil should only run when cursor is HIDDEN (not visible)
    CURSORINFO cursorInfo = {};
    cursorInfo.cbSize = sizeof(CURSORINFO);

    if (GetCursorInfo(&cursorInfo)) {
        // Check if cursor is showing (visible)
        bool isVisible = (cursorInfo.flags & CURSOR_SHOWING) != 0;

        // Additional check: if cursor handle is not null and showing
        return isVisible && cursorInfo.hCursor != nullptr;
    }

    // If we can't determine cursor state, assume it's not visible to be safe
    return false;
}

// Template approach - no calibration needed

void RecoilControlService::setCompensateHipfire(bool enabled)
{
    m_compensateHipfire = enabled;
    std::cout << "Compensate hipfire " << (enabled ? "enabled" : "disabled") << std::endl;
}

void RecoilControlService::setHipfireMode(const std::string& mode)
{
    m_hipfireMode = mode;
    std::cout << "Hipfire mode set to: " << mode << std::endl;
}

void RecoilControlService::setHorizontalRandomization(int value)
{
    m_horizontalRandomization = std::clamp(value, 0, 100);
    std::cout << "Horizontal randomization set to: " << m_horizontalRandomization << "%" << std::endl;
}

void RecoilControlService::setVerticalRandomization(int value)
{
    m_verticalRandomization = std::clamp(value, 0, 100);
    // Reduced logging - only log significant changes
    static int lastValue = -1;
    if (abs(value - lastValue) >= 10) {
        std::cout << "Vertical randomization: " << m_verticalRandomization << "%" << std::endl;
        lastValue = value;
    }
}

void RecoilControlService::setHumanizationEnabled(bool enabled)
{
    m_humanizationEnabled = enabled;
    std::cout << "Humanization " << (enabled ? "enabled" : "disabled") << std::endl;
}

void RecoilControlService::setHumanizationRandomization(int value)
{
    m_humanizationRandomization = std::clamp(value, 0, 100);
    // Reduced logging - only log significant changes
    static int lastValue = -1;
    if (abs(value - lastValue) >= 10) {
        std::cout << "Humanization randomization: " << m_humanizationRandomization << "%" << std::endl;
        lastValue = value;
    }
}

void RecoilControlService::setHumanizationLevel(int value)
{
    m_humanizationLevel = std::clamp(value, 0, 100);
    std::cout << "Humanization level set to: " << m_humanizationLevel << "%" << std::endl;
}

bool RecoilControlService::isKeyPressed(int vkCode) const
{
    return (GetAsyncKeyState(vkCode) & 0x8000) != 0;
}

bool RecoilControlService::isKeyJustPressed(const std::string& action)
{
    auto it = m_keybinds.find(action);
    if (it == m_keybinds.end()) return false;

    bool currentState = isKeyPressed(it->second);
    bool wasPressed = m_keyStates[action];
    m_keyStates[action] = currentState;

    // Return true only on the transition from not pressed to pressed
    return currentState && !wasPressed;
}

int RecoilControlService::getVirtualKeyCode(const std::string& keyName) const
{
    // Convert key name to virtual key code
    if (keyName == "F1") return VK_F1;
    if (keyName == "F2") return VK_F2;
    if (keyName == "F3") return VK_F3;
    if (keyName == "F4") return VK_F4;
    if (keyName == "F5") return VK_F5;
    if (keyName == "F6") return VK_F6;
    if (keyName == "F7") return VK_F7;
    if (keyName == "F8") return VK_F8;
    if (keyName == "F9") return VK_F9;
    if (keyName == "F10") return VK_F10;
    if (keyName == "F11") return VK_F11;
    if (keyName == "F12") return VK_F12;
    if (keyName == "Space") return VK_SPACE;
    if (keyName == "Enter") return VK_RETURN;
    if (keyName == "Escape") return VK_ESCAPE;
    if (keyName == "Tab") return VK_TAB;
    if (keyName == "Shift") return VK_SHIFT;
    if (keyName == "Control") return VK_CONTROL;
    if (keyName == "Alt") return VK_MENU;

    // Single character keys
    if (keyName.length() == 1) {
        char c = std::toupper(keyName[0]);
        if (c >= 'A' && c <= 'Z') return c;
        if (c >= '0' && c <= '9') return c;
    }

    return 0; // Unknown key
}

void RecoilControlService::processKeybinds()
{
    // Check auto code lock keybind
    if (m_autoCodeLockEnabled && isKeyJustPressed("autoCodeLock")) {
        executeAutoCodeLock();
    }

    // Check show/hide interface keybind
    if (isKeyJustPressed("showHideInterface")) {
        toggleInterface();
    }

    // Process anti-AFK
    if (m_antiAfkEnabled) {
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - m_lastAfkTime).count();
        if (elapsed >= m_afkInterval) {
            executeAntiAfk();
            m_lastAfkTime = now;
        }
    }
}

void RecoilControlService::executeAutoCodeLock()
{
    if (m_codeLockDigits.length() != 4) {
        std::cout << "Auto code lock: Invalid digits (need 4 digits)" << std::endl;
        return;
    }

    std::cout << "Auto code lock: Spamming code " << m_codeLockDigits << std::endl;

    // Spam the 4-digit code multiple times
    for (int i = 0; i < 10; i++) {
        for (char digit : m_codeLockDigits) {
            // Convert ASCII digit to virtual key code
            WORD vkCode = 0;
            if (digit >= '0' && digit <= '9') {
                vkCode = '0' + (digit - '0'); // VK_0 to VK_9
            } else {
                continue; // Skip invalid characters
            }

            // Simulate key press for each digit
            INPUT input = {};
            input.type = INPUT_KEYBOARD;
            input.ki.wVk = vkCode;
            input.ki.dwFlags = 0; // Key down
            SendInput(1, &input, sizeof(INPUT));

            input.ki.dwFlags = KEYEVENTF_KEYUP; // Key up
            SendInput(1, &input, sizeof(INPUT));

            Sleep(50); // Small delay between digits
        }

        // Press Enter after each code
        INPUT enterInput = {};
        enterInput.type = INPUT_KEYBOARD;
        enterInput.ki.wVk = VK_RETURN;
        enterInput.ki.dwFlags = 0;
        SendInput(1, &enterInput, sizeof(INPUT));

        enterInput.ki.dwFlags = KEYEVENTF_KEYUP;
        SendInput(1, &enterInput, sizeof(INPUT));

        Sleep(100); // Delay between attempts
    }
}

void RecoilControlService::executeAntiAfk()
{
    std::cout << "Anti-AFK: Moving mouse slightly" << std::endl;

    // Get current cursor position
    POINT currentPos;
    GetCursorPos(&currentPos);

    // Move mouse slightly (2 pixels) and back
    SetCursorPos(currentPos.x + 2, currentPos.y + 2);
    Sleep(50);
    SetCursorPos(currentPos.x, currentPos.y);
}

// ULTRA-OPTIMIZED: Send entire recoil sequence to ESP32 for hardware processing
void RecoilControlService::sendOptimizedRecoilSequence(float smoothing, int bulletDelay)
{
    if (!m_esp32Service || !m_esp32Service->isConnected()) {
        std::cout << "❌ ESP32 not connected - cannot send optimized sequence" << std::endl;
        return;
    }

    auto recoilPattern = m_currentWeapon.getRecoilPattern();
    if (recoilPattern.empty()) {
        return;
    }

    // Calculate all bullet positions using the same formula as individual bullets
    std::vector<std::pair<float, float>> movements;

    float sensitivity = m_recoilSettings.getSensitivity();
    float adsSensitivity = m_recoilSettings.getAdsSensitivity();
    float fov = m_recoilSettings.getFOV();
    float scale = -0.03f * (sensitivity * adsSensitivity * 3.0f) * (fov / 100.0f);
    float movementMultiplier = getMovementMultiplier();
    bool isCrouching = this->isCrouching() || this->isCrouchWalking();

    // Process all bullets in the pattern
    int patternSize = (int)recoilPattern.size();
    int weaponMaxBullets = (int)m_currentWeapon.getMaxBulletCount();
    int maxBullets = (patternSize < weaponMaxBullets) ? patternSize : weaponMaxBullets;

    for (int i = 0; i < maxBullets; ++i) {
        auto recoilVector = recoilPattern[i];

        // Apply template formula
        float pixelX = std::round(recoilVector.x / scale);
        float pixelY = std::round(recoilVector.y / scale);

        // Apply crouch modifier
        if (isCrouching) {
            pixelX *= 0.5f;
            pixelY *= 0.5f;
        }

        // Apply movement multiplier
        pixelX *= movementMultiplier;
        pixelY *= movementMultiplier;

        // CRITICAL FIX: Send delta movements, not absolute positions
        // ESP32 expects relative movement per bullet, not cumulative position
        movements.push_back({pixelX, pixelY});
    }

    // Send optimized sequence to ESP32
    bool success = m_esp32Service->sendRecoilSequence(movements, static_cast<int>(smoothing), bulletDelay);

    if (success) {
        std::cout << "🚀 Sent optimized sequence: " << movements.size() << " bullets, "
                  << smoothing << "% smoothness, " << bulletDelay << "ms delay" << std::endl;

        // Mark entire sequence as processed
        m_currentBulletIndex = maxBullets;
        m_recoilActive = false; // Sequence complete
    } else {
        std::cout << "❌ Failed to send optimized sequence - falling back to individual commands" << std::endl;
    }
}

void RecoilControlService::sendSmartBufferedBullet(float deltaX, float deltaY, float smoothing, int bulletDelay) {
    if (!m_esp32Service || !m_esp32Service->isConnected()) {
        return;
    }

    // SMART BUFFERED APPROACH: Send individual bullet with hardware smoothing
    // Format: RECOIL_SMOOTH deltaX,deltaY,smoothness,delay
    // This gives us live control (stops when shooting stops) + hardware smoothing

    int intDeltaX = static_cast<int>(std::round(deltaX));
    int intDeltaY = static_cast<int>(std::round(deltaY));
    int intSmoothness = static_cast<int>(smoothing);

    std::string command = "RECOIL_SMOOTH " + std::to_string(intDeltaX) + "," +
                         std::to_string(intDeltaY) + "," +
                         std::to_string(intSmoothness) + "," +
                         std::to_string(bulletDelay);

    std::cout << "ESP32: Smart buffered bullet (" << intDeltaX << "," << intDeltaY
              << ") with " << intSmoothness << "% smoothing" << std::endl;

    m_esp32Service->sendCommand(command);
}

// NEW: Weapon-based recoil system
void RecoilControlService::startWeaponBasedRecoil() {
    if (!m_esp32Service || !m_esp32Service->isConnected()) {
        return;
    }

    // Get current settings
    float smoothing = m_recoilSettings.getSmoothing();
    float sensitivity = m_recoilSettings.getSensitivity();
    int weaponId = m_currentWeapon.getId();

    // Send weapon start command to ESP32
    std::string command = "WEAPON_START " + std::to_string(weaponId) + "," +
                         std::to_string(sensitivity) + "," +
                         std::to_string(static_cast<int>(smoothing));

    std::cout << "🎯 Starting weapon-based recoil: " << m_currentWeapon.getName()
              << " (ID: " << weaponId << ", sens: " << sensitivity
              << ", smooth: " << smoothing << "%)" << std::endl;

    m_esp32Service->sendCommand(command);
}

void RecoilControlService::stopWeaponBasedRecoil() {
    if (!m_esp32Service || !m_esp32Service->isConnected()) {
        return;
    }

    std::cout << "🛑 Stopping weapon-based recoil" << std::endl;
    m_esp32Service->sendCommand("WEAPON_STOP");
}

void RecoilControlService::sendWeaponBasedBullet() {
    if (!m_esp32Service || !m_esp32Service->isConnected()) {
        return;
    }

    // Simply tell ESP32 to fire the next bullet in the weapon pattern
    // ESP32 handles all the recoil calculation, smoothing, and timing
    std::cout << "🔫 Firing bullet " << (m_currentBulletIndex + 1) << " (weapon-based)" << std::endl;
    m_esp32Service->sendCommand("WEAPON_BULLET");
}

void RecoilControlService::toggleInterface()
{
    m_interfaceVisible = !m_interfaceVisible;
    std::cout << "Interface " << (m_interfaceVisible ? "shown" : "hidden") << std::endl;

    // Send message to main window to hide/show interface
    // This will be handled by the main window's message processing
    // For now, just log the action - the main window would need to implement the actual hiding
}

} // namespace services
} // namespace octane
