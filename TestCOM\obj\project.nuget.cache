{"version": 2, "dgSpecHash": "5ZkQgeHrpJU=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Desktop\\recoil\\esp32-hid-firmware\\Custom farmer for ESP\\TestCOM\\TestCOM.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\runtime.android-arm.runtime.native.system.io.ports\\9.0.7\\runtime.android-arm.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.android-arm64.runtime.native.system.io.ports\\9.0.7\\runtime.android-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.android-x64.runtime.native.system.io.ports\\9.0.7\\runtime.android-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.android-x86.runtime.native.system.io.ports\\9.0.7\\runtime.android-x86.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-arm.runtime.native.system.io.ports\\9.0.7\\runtime.linux-arm.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-arm64.runtime.native.system.io.ports\\9.0.7\\runtime.linux-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-bionic-arm64.runtime.native.system.io.ports\\9.0.7\\runtime.linux-bionic-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-bionic-x64.runtime.native.system.io.ports\\9.0.7\\runtime.linux-bionic-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-musl-arm.runtime.native.system.io.ports\\9.0.7\\runtime.linux-musl-arm.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-musl-arm64.runtime.native.system.io.ports\\9.0.7\\runtime.linux-musl-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-musl-x64.runtime.native.system.io.ports\\9.0.7\\runtime.linux-musl-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.linux-x64.runtime.native.system.io.ports\\9.0.7\\runtime.linux-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.maccatalyst-arm64.runtime.native.system.io.ports\\9.0.7\\runtime.maccatalyst-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.maccatalyst-x64.runtime.native.system.io.ports\\9.0.7\\runtime.maccatalyst-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.io.ports\\9.0.7\\runtime.native.system.io.ports.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx-arm64.runtime.native.system.io.ports\\9.0.7\\runtime.osx-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.osx-x64.runtime.native.system.io.ports\\9.0.7\\runtime.osx-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.ports\\9.0.7\\system.io.ports.9.0.7.nupkg.sha512"], "logs": []}