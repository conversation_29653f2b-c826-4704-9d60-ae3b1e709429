#include "views/MainPageStyles.h"

namespace octane {
namespace views {

std::string MainPageStyles::getMainPageStyles()
{
    return R"(
        /* Clean Main Page Grid Layout */
        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 20px;
            height: calc(100vh - 120px);
            padding: 20px;
        }

        .main-card {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .main-card h3 {
            color: #ffffff;
            margin: 0 0 15px 0;
            font-size: 16px;
            font-weight: 600;
            border-bottom: 1px solid #444;
            padding-bottom: 8px;
        }

        /* Recoil Control Card */
        .control-row {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .toggle-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s;
        }

        .toggle-btn.disabled {
            background: #555;
            color: #ccc;
        }

        .toggle-btn.enabled {
            background: #00d4ff;
            color: #000;
        }

        .keybind-btn {
            padding: 12px 16px;
            background: #444;
            border: 1px solid #666;
            border-radius: 6px;
            color: #fff;
            cursor: pointer;
            min-width: 50px;
        }

        .keybind-btn:hover {
            background: #555;
        }

        .esp32-section {
            flex: 1;
        }

        .port-row {
            display: flex;
            gap: 8px;
            margin-bottom: 10px;
        }

        .port-select {
            flex: 1;
            padding: 8px;
            background: #333;
            border: 1px solid #555;
            border-radius: 4px;
            color: #fff;
        }

        .btn-small {
            padding: 8px 12px;
            background: #444;
            border: 1px solid #666;
            border-radius: 4px;
            color: #fff;
            cursor: pointer;
            font-size: 12px;
        }

        .btn-small:hover {
            background: #555;
        }

        .connection-row {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .connect-btn {
            padding: 10px 20px;
            background: #00d4ff;
            border: none;
            border-radius: 6px;
            color: #000;
            font-weight: bold;
            cursor: pointer;
        }

        .connect-btn:hover {
            background: #00b8e6;
        }

        .status-display {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-dot.disconnected {
            background: #ff6b6b;
        }

        .status-dot.connected {
            background: #51cf66;
        }

        /* Weapon Setup Card */
        .weapon-row {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .weapon-row label {
            color: #ccc;
            min-width: 80px;
            font-size: 14px;
        }

        .weapon-select {
            flex: 1;
            padding: 8px;
            background: #333;
            border: 1px solid #555;
            border-radius: 4px;
            color: #fff;
            margin-left: 10px;
        }

        /* Settings Card */
        .settings-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            flex: 1;
        }

        .setting-item {
            display: flex;
            flex-direction: column;
        }

        .setting-item label {
            color: #ccc;
            font-size: 12px;
            margin-bottom: 5px;
        }

        .setting-input {
            padding: 8px;
            background: #333;
            border: 1px solid #555;
            border-radius: 4px;
            color: #fff;
            font-size: 14px;
        }

        .checkbox-row {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 5px;
        }

        .checkbox-row input[type="checkbox"] {
            margin: 0;
        }

        .checkbox-row label {
            color: #ccc;
            font-size: 13px;
            margin: 0;
        }

        /* Humanization Card */
        .humanization-section {
            flex: 1;
        }

        .humanization-row {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .humanization-toggle {
            flex: 1;
            padding: 10px;
            background: #555;
            border: none;
            border-radius: 6px;
            color: #ccc;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s;
        }

        .humanization-toggle.enabled {
            background: #00d4ff;
            color: #000;
        }

        .humanization-keybind {
            padding: 10px 14px;
            background: #444;
            border: 1px solid #666;
            border-radius: 6px;
            color: #fff;
            cursor: pointer;
            min-width: 45px;
            font-size: 12px;
        }

        .humanization-keybind:hover {
            background: #555;
        }

        .humanization-sliders {
            margin-top: 15px;
        }

        .humanization-item {
            margin-bottom: 15px;
        }

        .humanization-item label {
            color: #ccc;
            font-size: 13px;
            display: block;
            margin-bottom: 6px;
        }

        .humanization-slider-group {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .humanization-slider {
            flex: 1;
            height: 5px;
            background: #444;
            border-radius: 3px;
            outline: none;
            -webkit-appearance: none;
        }

        .humanization-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 14px;
            height: 14px;
            background: #00d4ff;
            border-radius: 50%;
            cursor: pointer;
        }

        .humanization-slider::-moz-range-thumb {
            width: 14px;
            height: 14px;
            background: #00d4ff;
            border-radius: 50%;
            cursor: pointer;
            border: none;
        }

        .humanization-input {
            width: 45px;
            padding: 4px;
            background: #333;
            border: 1px solid #555;
            border-radius: 3px;
            color: #fff;
            text-align: center;
            font-size: 11px;
        }
    )";
}

} // namespace views
} // namespace octane
