#pragma once

#include <windows.h>
#include <wrl.h>
#include <memory>
#include <string>
#include <vector>
#include <functional>
#include "WebView2.h"

using namespace Microsoft::WRL;

namespace octane {

// Forward declarations
namespace core {
    class Application;
}

namespace services {
    class ESP32Service;
    class RecoilControlService;
    class SecurityService;
    class SoundService;
}

namespace utils {
    class ConfigManager;
}

namespace views {

/**
 * @brief Main application window using WebView2
 */
class MainWindow
{
public:
    explicit MainWindow(core::Application* app);
    ~MainWindow();

    /**
     * @brief Initialize the window
     */
    bool initialize();

    /**
     * @brief Show the window
     */
    void show();

    /**
     * @brief Hide the window
     */
    void hide();

    /**
     * @brief Process window messages
     */
    void processMessages();

    /**
     * @brief Check if window should close
     */
    bool shouldClose() const;

    /**
     * @brief Shutdown the window
     */
    void shutdown();

private:
    // Window management
    static LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, W<PERSON>RAM wParam, LPARAM lParam);
    bool createWindow();
    void createControls();
    void updateControlPositions();

    // WebView2 management
    void initializeWebView();
    void loadHtmlContent();
    void handleWebViewMessage(const std::string& message);
    void handleAdditionalMessages(const std::string& message);
    void handleFeatureMessages(const std::string& message);
    void handleMoreFeatureMessages(const std::string& message);
    void handleRandomizationMessages(const std::string& message);
    void handleLicenseValidation(const std::string& key);
    void sendLicenseInfoToWebView();

    // License management
    bool validateLicenseWithVPS(const std::string& key);
    void showLicenseModal();
    void hideLicenseModal();
    void handleRefreshPorts();
    void handleESP32Connection(const std::string& message);
    void handleESP32Disconnection();



    // Config management handlers
    void handleSaveConfig(const std::string& message);
    void handleLoadConfig(const std::string& message);
    void handleDeleteConfig(const std::string& message);
    void handleRefreshConfigs();
    void handleAutoLoadConfigChanged(const std::string& message);
    void showLicenseError(const std::string& error);
    std::vector<std::string> getAvailableComPorts();

    // UI rendering
    void renderUI(HDC hdc);

    // Application reference
    core::Application* m_application = nullptr;

    // Services
    std::unique_ptr<services::ESP32Service> m_esp32Service;
    std::unique_ptr<services::RecoilControlService> m_recoilService;
    std::unique_ptr<services::SecurityService> m_securityService;
    std::unique_ptr<services::SoundService> m_soundService;
    std::unique_ptr<utils::ConfigManager> m_configManager;

    // Windows API
    HWND m_hwnd = nullptr;
    HINSTANCE m_hInstance = nullptr;

    // WebView2 control
    ComPtr<ICoreWebView2Controller> m_webViewController;
    ComPtr<ICoreWebView2> m_coreWebView;
    ComPtr<ICoreWebView2> m_webView;  // Compatibility alias for m_coreWebView

    // Window state
    bool m_initialized = false;
    bool m_shouldClose = false;

    // Constants
    static constexpr int WINDOW_WIDTH = 900;
    static constexpr int WINDOW_HEIGHT = 600;
};

} // namespace views
} // namespace octane