/*
 * ESP32-S2 HID Mouse Firmware - Octane Recoil Control System
 * Version: 2.2.0 - ULTRA-OPTIMIZED FOR RECOIL CONTROL
 * Target: ESP32-S2 Mini (Lolin S2 Mini)
 *
 * OPTIMIZATIONS:
 * - Zero-delay main loop for maximum responsiveness
 * - Immediate command acknowledgments
 * - Reduced heartbeat interference
 * - Priority-based task scheduling
 * - Ultra-fast mouse movement processing
 */

#include <Arduino.h>
#include "config.h"
#include "led_controller.h"
#include "usb_manager.h"
#include "command_processor.h"
#include "system_info.h"

// External USB Serial reference
extern USBCDC USBSerial;

// ===== SETUP FUNCTION =====
void setup() {
    // Initialize USB serial communication (will be handled by USB manager)
    
    // Initialize all components
    systemInfo.begin();
    ledController.begin();
    usbManager.begin();
    commandProcessor.begin();
    
    // Print system information
    systemInfo.printSystemInfo();
    
    // Set initial LED state
    ledController.setState(LED_WAITING);
    
    // Send boot sequence messages
    systemInfo.sendBootSequence();
    
    // Set ready state
    ledController.setState(LED_CONNECTED);
    
    USBSerial.println("Octane ESP32-S2 HID Mouse v" + String(FIRMWARE_VERSION) + " Ready - ULTRA-OPTIMIZED");
}

// ===== MAIN LOOP - ULTRA-OPTIMIZED FOR RECOIL =====
void loop() {
    // PRIORITY 1: Process serial commands IMMEDIATELY (zero delay)
    // This is the most critical for recoil control
    commandProcessor.processSerialCommands();
    
    // PRIORITY 2: Process command queue IMMEDIATELY (zero delay)
    // Execute mouse movements as fast as possible
    commandProcessor.processCommandQueue();
    
    // PRIORITY 3: Update LED pattern (low priority, throttled)
    static uint32_t lastLedUpdate = 0;
    if (millis() - lastLedUpdate > 50) {  // Update LED only every 50ms
        ledController.update();
        lastLedUpdate = millis();
    }
    
    // PRIORITY 4: Send heartbeat messages (lowest priority, heavily throttled)
    static uint32_t lastHeartbeatCheck = 0;
    if (millis() - lastHeartbeatCheck > 1000) {  // Check heartbeat only every 1 second
        commandProcessor.sendHeartbeat();
        lastHeartbeatCheck = millis();
    }
    
    // ZERO DELAY - Maximum responsiveness for recoil control
    // No delay() call - ESP32 can handle this without watchdog issues
    // This gives us sub-millisecond response times for mouse movements
}
