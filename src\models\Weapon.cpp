#include "models/Weapon.h"

namespace octane {
namespace models {

Weapon::Weapon()
    : m_name("")
    , m_id(0)
    , m_delay(0)
    , m_maxBulletCount(0)
    , m_hipfireScale(1.0f)
    , m_isAutomatic(false)
{
}

Weapon::Weapon(const std::string& name, int id, unsigned int delay, unsigned int maxBulletCount,
               float hipfireScale, bool isAutomatic, const std::vector<Vector2>& recoilPattern)
    : m_name(name)
    , m_id(id)
    , m_delay(delay)
    , m_maxBulletCount(maxBulletCount)
    , m_hipfireScale(hipfireScale)
    , m_isAutomatic(isAutomatic)
    , m_recoilPattern(recoilPattern)
{
}

} // namespace models
} // namespace octane
