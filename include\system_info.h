#ifndef SYSTEM_INFO_H
#define SYSTEM_INFO_H

#include <Arduino.h>
#include <esp_system.h>

// ===== SYSTEM INFO CLASS =====
class SystemInfo {
private:
    uint32_t bootTime;

public:
    SystemInfo();
    void begin();
    void printSystemInfo();
    uint32_t getUptime() const;
    size_t getFreeHeap() const;
    String getChipModel() const;
    uint32_t getCpuFreqMHz() const;
    String getResetReason() const;
    void sendBootSequence();

private:
    void debugPrint(const String& message);
};

// Global system info instance
extern SystemInfo systemInfo;

#endif // SYSTEM_INFO_H
