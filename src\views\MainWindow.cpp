#include "views/MainWindow.h"
#include "views/MainWindowHtml.h"
#include "models/WeaponData.h"
#include "security/LicenseManager.h"
#include "services/ESP32Service_NoQt.h"
#include "services/RecoilControlService.h"
#include "services/SecurityService.h"
#include "services/SoundService.h"
#include "utils/ConfigManager.h"
#include "config/AppConfig.h"
#include <iostream>
#include <wrl.h>
#include <algorithm>
#include "WebView2.h"

#ifdef _WIN32
#include <windows.h>
#endif

using namespace Microsoft::WRL;

namespace octane {
namespace views {

MainWindow::MainWindow(core::Application* app)
    : m_application(app)
    , m_hwnd(nullptr)
    , m_hInstance(GetModuleHandle(nullptr))
    , m_initialized(false)
    , m_shouldClose(false)
{
    // Initialize application configuration
    auto& config = config::AppConfig::getInstance();
    config.loadFromFile();
    std::cout << "AppConfig loaded - Mode: " << (config.isDevelopmentMode() ? "DEVELOPMENT" : "PRODUCTION") << std::endl;
    // Initialize ESP32Service
    m_esp32Service = std::make_unique<services::ESP32Service>();
    if (m_esp32Service) {
        m_esp32Service->initialize();
    }

    // Initialize RecoilControlService
    m_recoilService = std::make_unique<services::RecoilControlService>();
    if (m_recoilService) {
        m_recoilService->initialize();
        // Connect ESP32 service to recoil service
        m_recoilService->setESP32Service(std::shared_ptr<services::ESP32Service>(m_esp32Service.get(), [](services::ESP32Service*){}));
        // Start the recoil control loop
        m_recoilService->startRecoilControl();
        std::cout << "RecoilControlService initialized and started" << std::endl;
    }

    // Initialize SecurityService
    m_securityService = std::make_unique<services::SecurityService>();
    if (m_securityService) {
        m_securityService->initialize();
        m_securityService->startMonitoring();
        std::cout << "SecurityService initialized and monitoring started" << std::endl;
    }

    // Initialize SoundService
    m_soundService = std::make_unique<services::SoundService>();
    if (m_soundService) {
        m_soundService->initialize();
    }

    // Initialize ConfigManager
    m_configManager = std::make_unique<utils::ConfigManager>();
    if (m_configManager) {
        // Try to load saved COM port
        std::string savedComPort = m_configManager->loadComPort();
        if (!savedComPort.empty()) {
            std::cout << "Saved COM port found: " << savedComPort << std::endl;
        }

        // Try to auto-load config
        auto [configName, autoLoad] = m_configManager->loadAutoLoadConfig();
        if (autoLoad && !configName.empty()) {
            models::RecoilSettings settings;
            if (m_configManager->loadConfig(settings, configName) && m_recoilService) {
                // Apply loaded settings to recoil service
                m_recoilService->setSensitivity(settings.getSensitivity());
                m_recoilService->setAdsSensitivity(settings.getAdsSensitivity());
                m_recoilService->setFOV(settings.getFOV());
                std::cout << "Auto-loaded config: " << configName << std::endl;
            }
        }

        std::cout << "ConfigManager initialized" << std::endl;
    }
}

MainWindow::~MainWindow()
{
    shutdown();
}

bool MainWindow::initialize()
{
    std::cout << "MainWindow::initialize() called" << std::endl;

    if (m_initialized) {
        std::cout << "Already initialized" << std::endl;
        return true;
    }

    if (!createWindow()) {
        std::cerr << "Failed to create main window" << std::endl;
        return false;
    }

    std::cout << "Window created successfully" << std::endl;

    // Initialize WebView2
    initializeWebView();

    m_initialized = true;
    std::cout << "MainWindow initialization complete" << std::endl;
    return true;
}

void MainWindow::show()
{
    std::cout << "MainWindow::show() called" << std::endl;
    if (m_hwnd) {
        ShowWindow(m_hwnd, SW_SHOW);
        UpdateWindow(m_hwnd);
        std::cout << "Window shown successfully" << std::endl;
    } else {
        std::cout << "ERROR: m_hwnd is null!" << std::endl;
    }
}

void MainWindow::hide()
{
    if (m_hwnd) {
        ShowWindow(m_hwnd, SW_HIDE);
    }
}

bool MainWindow::shouldClose() const
{
    if (m_shouldClose) {
        std::cout << "Window should close" << std::endl;
    }
    return m_shouldClose;
}

void MainWindow::processMessages()
{
    MSG msg;
    while (PeekMessage(&msg, nullptr, 0, 0, PM_REMOVE)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
}

void MainWindow::shutdown()
{
    std::cout << "MainWindow::shutdown() called" << std::endl;

    // Clean up WebView2 first
    if (m_webView) {
        m_webView.Reset();
    }

    if (m_webViewController) {
        m_webViewController->Close();
        m_webViewController.Reset();
    }

    if (m_hwnd) {
        DestroyWindow(m_hwnd);
        m_hwnd = nullptr;
    }

    m_initialized = false;
    std::cout << "MainWindow shutdown complete" << std::endl;
}

bool MainWindow::createWindow()
{
    const wchar_t* className = L"OctaneRecoilController";

    // Register window class
    WNDCLASSEX wc = {};
    wc.cbSize = sizeof(WNDCLASSEX);
    wc.style = CS_HREDRAW | CS_VREDRAW;
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = m_hInstance;
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wc.lpszClassName = className;
    wc.hIcon = LoadIcon(nullptr, IDI_APPLICATION);
    wc.hIconSm = LoadIcon(nullptr, IDI_APPLICATION);

    if (!RegisterClassEx(&wc)) {
        return false;
    }

    // Create window
    m_hwnd = CreateWindowEx(
        0,
        className,
        L"Octane Recoil Controller v4.2.1",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT,
        800, 600,
        nullptr,
        nullptr,
        m_hInstance,
        this
    );

    return m_hwnd != nullptr;
}

void MainWindow::initializeWebView()
{
    std::cout << "Initializing WebView2..." << std::endl;

    if (!m_hwnd) {
        std::cerr << "ERROR: Window handle is null, cannot initialize WebView2" << std::endl;
        return;
    }

    // Create WebView2 environment and controller
    HRESULT hr = CreateCoreWebView2EnvironmentWithOptions(nullptr, nullptr, nullptr,
        Callback<ICoreWebView2CreateCoreWebView2EnvironmentCompletedHandler>(
            [this](HRESULT result, ICoreWebView2Environment* env) -> HRESULT {
                if (SUCCEEDED(result) && env) {
                    // Create WebView2 controller
                    env->CreateCoreWebView2Controller(m_hwnd,
                        Callback<ICoreWebView2CreateCoreWebView2ControllerCompletedHandler>(
                            [this](HRESULT result, ICoreWebView2Controller* controller) -> HRESULT {
                                if (SUCCEEDED(result) && controller) {
                                    m_webViewController = controller;
                                    HRESULT hr = m_webViewController->get_CoreWebView2(&m_coreWebView);
                                    if (FAILED(hr) || !m_coreWebView) {
                                        std::cerr << "Failed to get CoreWebView2. HRESULT: 0x" << std::hex << hr << std::endl;
                                        return hr;
                                    }

                                    // Store the webview reference for compatibility
                                    m_webView = m_coreWebView;

                                    // Resize WebView2 to fill the window
                                    RECT bounds;
                                    GetClientRect(m_hwnd, &bounds);
                                    m_webViewController->put_Bounds(bounds);

                                    // Set up message handler
                                    m_webView->add_WebMessageReceived(
                                        Callback<ICoreWebView2WebMessageReceivedEventHandler>(
                                            [this](ICoreWebView2* sender, ICoreWebView2WebMessageReceivedEventArgs* args) -> HRESULT {
                                                try {
                                                    if (!args) {
                                                        std::cerr << "WebMessageReceivedEventArgs is null" << std::endl;
                                                        return E_INVALIDARG;
                                                    }

                                                    LPWSTR message = nullptr;
                                                    HRESULT hr = args->TryGetWebMessageAsString(&message);
                                                    if (FAILED(hr) || !message) {
                                                        std::cerr << "Failed to get web message string. HRESULT: 0x" << std::hex << hr << std::endl;
                                                        return hr;
                                                    }

                                                    // Convert to std::string and handle
                                                    std::wstring wstr(message);
                                                    std::string str(wstr.begin(), wstr.end());
                                                    handleWebViewMessage(str);

                                                    // Free the message string
                                                    CoTaskMemFree(message);

                                                    return S_OK;
                                                } catch (const std::exception& e) {
                                                    std::cerr << "Exception in WebMessageReceived handler: " << e.what() << std::endl;
                                                    return E_FAIL;
                                                } catch (...) {
                                                    std::cerr << "Unknown exception in WebMessageReceived handler" << std::endl;
                                                    return E_FAIL;
                                                }
                                            }).Get(), nullptr);

                                    // Load HTML content
                                    loadHtmlContent();

                                    std::cout << "WebView2 initialized successfully!" << std::endl;
                                } else {
                                    std::cerr << "Failed to create WebView2 controller. HRESULT: 0x" << std::hex << result << std::endl;
                                }
                                return S_OK;
                            }).Get());
                } else {
                    std::cerr << "Failed to create WebView2 environment. HRESULT: 0x" << std::hex << result << std::endl;
                }
                return S_OK;
            }).Get());

    if (FAILED(hr)) {
        std::cerr << "Failed to create WebView2 environment with options. HRESULT: 0x" << std::hex << hr << std::endl;
    }
}

void MainWindow::loadHtmlContent()
{
    std::cout << "Loading HTML content..." << std::endl;

    if (!m_webView) {
        std::cout << "WebView2 not ready yet, will load content when ready" << std::endl;
        return;
    }

    if (!m_webView.Get()) {
        std::cerr << "ERROR: WebView2 ComPtr is null!" << std::endl;
        return;
    }

    // Get the complete HTML content
    std::string htmlContent = MainWindowHtml::getHtmlContent();

    // Get available weapons from hardcoded data
    auto weapons = models::WeaponData::getAllWeapons();
    std::cout << "Available weapons:" << std::endl;
    for (const auto& weapon : weapons) {
        std::cout << "  - " << weapon.getName() << " (ID: " << weapon.getId() << ")" << std::endl;
    }

    // Convert to wide string for WebView2
    std::wstring wHtmlContent(htmlContent.begin(), htmlContent.end());

    // Load HTML content into WebView2
    HRESULT hr = m_webView->NavigateToString(wHtmlContent.c_str());
    if (SUCCEEDED(hr)) {
        std::cout << "HTML content loaded into WebView2 (" << htmlContent.length() << " characters)" << std::endl;
    } else {
        std::cerr << "Failed to navigate to HTML content. HRESULT: 0x" << std::hex << hr << std::endl;
    }
}

// Message handling moved to MainWindowMessageHandlers.cpp



void MainWindow::handleLicenseValidation(const std::string& key)
{
    std::cout << "Validating license key: " << key << std::endl;

    if (key.empty()) {
        showLicenseError("Please enter a license key");
        return;
    }

    // Show validating message
    if (m_webView) {
        std::wstring script = L"document.getElementById('license-error').textContent = 'Validating license key...'; document.getElementById('license-error').style.color = '#00d4ff';";
        m_webView->ExecuteScript(script.c_str(), nullptr);
    }

    // TODO: In production, validate against VPS server
    // For now, use local validation
    bool isValid = validateLicenseWithVPS(key);

    if (isValid) {
        std::cout << "License key validated successfully" << std::endl;
        bool setResult = security::LicenseManager::setLicenseKey(key);
        if (setResult) {
            hideLicenseModal();
            sendLicenseInfoToWebView();
        } else {
            showLicenseError("Failed to set license key");
        }
    } else {
        std::cout << "License key validation failed" << std::endl;
        showLicenseError("Invalid license key. Please check your key and try again.");
    }
}

bool MainWindow::validateLicenseWithVPS(const std::string& key)
{
    std::cout << "Validating license key with VPS: " << key << std::endl;

    // Check configuration mode
    auto& config = config::AppConfig::getInstance();

    if (config.isDevelopmentMode()) {
        std::cout << "Development mode - using relaxed license validation" << std::endl;
        // In development mode, accept any non-empty key
        if (!key.empty()) {
            std::cout << "Development license key accepted: " << key << std::endl;
            return true;
        }
        return false;
    }

    // Production mode - full validation
    std::cout << "Production mode - using strict license validation" << std::endl;

    // Simulate network delay like C# version
    Sleep(1000);

    // For now, accept any key that looks like a real license key
    // In production, this would make an HTTP POST to https://octane.lol/api/validate
    // with JSON: {"licenseKey": key, "hardwareId": getHardwareId()}

    // Basic validation - key should not be empty and have reasonable length
    if (key.empty()) {
        std::cout << "Empty license key" << std::endl;
        return false;
    }

    if (key.length() < 3) {
        std::cout << "License key too short" << std::endl;
        return false;
    }

    // For testing, accept common license key patterns
    // Real keys from your system might look like: "ABC-DEF-GHI", "ABCDEFGHIJK", etc.

    // Accept test keys
    if (key == "TEST" || key == "test" || key == "TEST123" || key == "TESTKEY") {
        std::cout << "Test license key accepted" << std::endl;
        return true;
    }

    // Accept keys with dashes (common format)
    if (key.find('-') != std::string::npos && key.length() >= 7) {
        std::cout << "License key with dashes accepted" << std::endl;
        return true;
    }

    // Accept alphanumeric keys of reasonable length
    if (key.length() >= 8) {
        bool hasAlpha = false;
        bool hasNum = false;
        for (char c : key) {
            if (std::isalpha(c)) hasAlpha = true;
            if (std::isdigit(c)) hasNum = true;
        }
        if (hasAlpha || hasNum) {
            std::cout << "Valid license key format accepted" << std::endl;
            return true;
        }
    }

    std::cout << "License key format not recognized" << std::endl;
    return false;
}

void MainWindow::sendLicenseInfoToWebView()
{
    if (!m_webView) return;

    auto licenseInfo = security::LicenseManager::getCurrentLicense();

    // Create JavaScript to update license info
    std::wstring script = L"updateLicenseInfo({";
    script += L"isValid: " + (licenseInfo.isValid ? std::wstring(L"true") : std::wstring(L"false")) + L",";
    script += L"type: '" + std::wstring(licenseInfo.keyType.begin(), licenseInfo.keyType.end()) + L"',";
    script += L"daysRemaining: " + std::to_wstring(licenseInfo.daysRemaining) + L",";
    script += L"user: '" + std::wstring(licenseInfo.userName.begin(), licenseInfo.userName.end()) + L"'";
    script += L"});";

    m_webView->ExecuteScript(script.c_str(), nullptr);
}

void MainWindow::showLicenseModal()
{
    if (!m_webView) return;

    // Initialize the new simple license system
    std::wstring script = L"console.log('🔐 Initializing new license system...'); "
                          L"initializeLicenseSystem();";
    m_webView->ExecuteScript(script.c_str(), nullptr);
}

void MainWindow::hideLicenseModal()
{
    if (!m_webView) return;

    std::wstring script = L"document.getElementById('license-modal').style.display = 'none'; document.getElementById('main-container').style.display = 'block';";
    m_webView->ExecuteScript(script.c_str(), nullptr);
}

void MainWindow::handleRefreshPorts()
{
    if (!m_webView) return;

    try {
        // Get COM ports directly using Windows Registry
        std::vector<std::string> ports = getAvailableComPorts();

        // Build JavaScript array of port names
        std::string portsArray = "[";
        for (size_t i = 0; i < ports.size(); ++i) {
            if (i > 0) portsArray += ",";
            portsArray += "\"" + ports[i] + "\"";
        }
        portsArray += "]";

        // Call JavaScript function to update the dropdown
        std::string script = "updateComPorts(" + portsArray + ");";
        std::wstring wScript(script.begin(), script.end());

        m_webView->ExecuteScript(wScript.c_str(), nullptr);

        std::cout << "Sent " << ports.size() << " COM ports to WebView" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Error refreshing ports: " << e.what() << std::endl;
    }
}

std::vector<std::string> MainWindow::getAvailableComPorts()
{
    std::vector<std::string> ports;

    #ifdef _WIN32
    // Windows implementation using Registry
    HKEY hKey;
    if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, "HARDWARE\\DEVICEMAP\\SERIALCOMM", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        DWORD index = 0;
        char valueName[256];
        DWORD valueNameSize = sizeof(valueName);
        char data[256];
        DWORD dataSize = sizeof(data);
        DWORD type;

        while (RegEnumValueA(hKey, index, valueName, &valueNameSize, NULL, &type, (LPBYTE)data, &dataSize) == ERROR_SUCCESS) {
            if (type == REG_SZ) {
                ports.push_back(std::string(data));
            }

            // Reset sizes for next iteration
            valueNameSize = sizeof(valueName);
            dataSize = sizeof(data);
            index++;
        }

        RegCloseKey(hKey);
    }

    // Sort the ports
    std::sort(ports.begin(), ports.end());
    #endif

    return ports;
}

void MainWindow::handleESP32Connection(const std::string& message)
{
    if (!m_webView) return;

    try {
        // Extract port name from JSON message
        std::string portName;
        size_t portPos = message.find("\"port\":\"");
        if (portPos != std::string::npos) {
            portPos += 8; // Skip "port":"
            size_t endPos = message.find("\"", portPos);
            if (endPos != std::string::npos) {
                portName = message.substr(portPos, endPos - portPos);
            }
        }

        if (portName.empty()) {
            std::cout << "No port specified for ESP32 connection" << std::endl;
            return;
        }

        std::cout << "Attempting to connect to ESP32 on port: " << portName << std::endl;

        // Use the actual ESP32Service to connect
        bool connected = false;
        if (m_esp32Service) {
            connected = m_esp32Service->connectToDevice(portName);
        } else {
            // Fallback simulation if service not available
            connected = true;
        }

        if (connected) {
            // Send success response back to WebView using PostWebMessageAsString
            std::string response = R"({"type":"esp32Connected","success":true,"port":")" + portName + R"(","message":"ESP32 connected successfully"})";
            std::wstring wResponse(response.begin(), response.end());

            if (m_webView) {
                m_webView->PostWebMessageAsString(wResponse.c_str());
            }

            std::cout << "ESP32 connected successfully to " << portName << std::endl;
        } else {
            // Send failure response back to WebView
            std::string response = R"({"type":"esp32Connected","success":false,"port":")" + portName + R"(","message":"Failed to connect to ESP32"})";
            std::wstring wResponse(response.begin(), response.end());

            if (m_webView) {
                m_webView->PostWebMessageAsString(wResponse.c_str());
            }

            std::cout << "Failed to connect to ESP32 on " << portName << std::endl;
        }
    } catch (const std::exception& e) {
        std::cerr << "Error connecting to ESP32: " << e.what() << std::endl;

        // Send error response back to WebView
        std::string response = R"({"type":"esp32Connected","success":false,"message":"Connection error: )" + std::string(e.what()) + R"("})";
        std::wstring wResponse(response.begin(), response.end());

        if (m_webView) {
            m_webView->PostWebMessageAsString(wResponse.c_str());
        }
    }
}

void MainWindow::handleESP32Disconnection()
{
    if (!m_webView) return;

    try {
        std::cout << "Disconnecting ESP32..." << std::endl;

        // Use the actual ESP32Service to disconnect
        if (m_esp32Service) {
            m_esp32Service->disconnect();
        }

        // Send disconnection response back to WebView
        std::string response = R"({"type":"esp32Disconnected","success":true,"message":"ESP32 disconnected successfully"})";
        std::wstring wResponse(response.begin(), response.end());

        if (m_webView) {
            m_webView->PostWebMessageAsString(wResponse.c_str());
        }

        std::cout << "ESP32 disconnected successfully" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Error disconnecting ESP32: " << e.what() << std::endl;

        // Send error response back to WebView
        std::string response = R"({"type":"esp32Disconnected","success":false,"message":"Disconnection error: )" + std::string(e.what()) + R"("})";
        std::wstring wResponse(response.begin(), response.end());

        if (m_webView) {
            m_webView->PostWebMessageAsString(wResponse.c_str());
        }
    }
}

void MainWindow::showLicenseError(const std::string& error)
{
    if (!m_webView) return;

    std::wstring errorW(error.begin(), error.end());
    std::wstring script = L"document.getElementById('license-error').textContent = '" + errorW + L"'; document.getElementById('license-error').style.color = '#ff4444';";
    m_webView->ExecuteScript(script.c_str(), nullptr);
}

void MainWindow::handleSaveConfig(const std::string& message)
{
    if (!m_configManager || !m_recoilService) return;

    // Extract config name from message
    size_t nameStart = message.find("\"configName\":\"");
    if (nameStart != std::string::npos) {
        nameStart += 14; // Length of "configName":"
        size_t nameEnd = message.find("\"", nameStart);
        if (nameEnd != std::string::npos) {
            std::string configName = message.substr(nameStart, nameEnd - nameStart);

            // Get current settings from recoil service
            models::RecoilSettings settings; // This will have default values
            // TODO: Get actual current settings from UI or recoil service

            if (m_configManager->saveConfig(settings, configName)) {
                std::cout << "Config saved successfully: " << configName << std::endl;
                handleRefreshConfigs(); // Refresh the config list
            } else {
                std::cout << "Failed to save config: " << configName << std::endl;
            }
        }
    }
}

void MainWindow::handleLoadConfig(const std::string& message)
{
    if (!m_configManager || !m_recoilService) return;

    // Extract config name from message
    size_t nameStart = message.find("\"configName\":\"");
    if (nameStart != std::string::npos) {
        nameStart += 14; // Length of "configName":"
        size_t nameEnd = message.find("\"", nameStart);
        if (nameEnd != std::string::npos) {
            std::string configName = message.substr(nameStart, nameEnd - nameStart);

            models::RecoilSettings settings;
            if (m_configManager->loadConfig(settings, configName)) {
                // Apply loaded settings to recoil service
                m_recoilService->setSensitivity(settings.getSensitivity());
                m_recoilService->setAdsSensitivity(settings.getAdsSensitivity());
                m_recoilService->setFOV(settings.getFOV());

                std::cout << "Config loaded successfully: " << configName << std::endl;

                // TODO: Update UI with loaded values
            } else {
                std::cout << "Failed to load config: " << configName << std::endl;
            }
        }
    }
}

void MainWindow::handleDeleteConfig(const std::string& message)
{
    if (!m_configManager) return;

    // Extract config name from message
    size_t nameStart = message.find("\"configName\":\"");
    if (nameStart != std::string::npos) {
        nameStart += 14; // Length of "configName":"
        size_t nameEnd = message.find("\"", nameStart);
        if (nameEnd != std::string::npos) {
            std::string configName = message.substr(nameStart, nameEnd - nameStart);

            if (m_configManager->deleteConfig(configName)) {
                std::cout << "Config deleted successfully: " << configName << std::endl;
                handleRefreshConfigs(); // Refresh the config list
            } else {
                std::cout << "Failed to delete config: " << configName << std::endl;
            }
        }
    }
}

void MainWindow::handleRefreshConfigs()
{
    if (!m_configManager) return;

    auto configs = m_configManager->getAvailableConfigs();

    // Build JavaScript to update the config list
    std::string script = "const configList = document.getElementById('config-list');\n";
    script += "configList.innerHTML = '<option value=\"\">Select config...</option>';\n";

    for (const auto& config : configs) {
        script += "const option = document.createElement('option');\n";
        script += "option.value = '" + config + "';\n";
        script += "option.textContent = '" + config + "';\n";
        script += "configList.appendChild(option);\n";
    }

    // Execute the script to update UI
    if (m_webView) {
        std::wstring wScript(script.begin(), script.end());
        m_webView->ExecuteScript(wScript.c_str(), nullptr);
    }

    std::cout << "Config list refreshed, found " << configs.size() << " configs" << std::endl;
}

void MainWindow::handleAutoLoadConfigChanged(const std::string& message)
{
    if (!m_configManager) return;

    // Extract enabled state and config name
    bool enabled = message.find("\"enabled\":true") != std::string::npos;

    size_t nameStart = message.find("\"configName\":\"");
    std::string configName;
    if (nameStart != std::string::npos) {
        nameStart += 14; // Length of "configName":"
        size_t nameEnd = message.find("\"", nameStart);
        if (nameEnd != std::string::npos) {
            configName = message.substr(nameStart, nameEnd - nameStart);
        }
    }

    if (m_configManager->saveAutoLoadConfig(configName, enabled)) {
        std::cout << "Auto-load config " << (enabled ? "enabled" : "disabled")
                  << " for: " << configName << std::endl;
    }
}

LRESULT CALLBACK MainWindow::WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    MainWindow* window = nullptr;

    if (uMsg == WM_NCCREATE) {
        CREATESTRUCT* cs = reinterpret_cast<CREATESTRUCT*>(lParam);
        window = reinterpret_cast<MainWindow*>(cs->lpCreateParams);
        SetWindowLongPtr(hwnd, GWLP_USERDATA, reinterpret_cast<LONG_PTR>(window));
    } else {
        window = reinterpret_cast<MainWindow*>(GetWindowLongPtr(hwnd, GWLP_USERDATA));
    }

    if (window) {
        switch (uMsg) {
        case WM_CLOSE:
            std::cout << "WM_CLOSE received - user closed window" << std::endl;
            window->m_shouldClose = true;
            return 0;
        case WM_SIZE:
            // Resize WebView2 when window is resized
            if (window->m_webViewController && window->m_webViewController.Get()) {
                RECT bounds;
                GetClientRect(hwnd, &bounds);
                HRESULT hr = window->m_webViewController->put_Bounds(bounds);
                if (FAILED(hr)) {
                    std::cerr << "Failed to resize WebView2. HRESULT: 0x" << std::hex << hr << std::endl;
                }
            }
            return 0;
        }
    }

    return DefWindowProc(hwnd, uMsg, wParam, lParam);
}

} // namespace views
} // namespace octane
