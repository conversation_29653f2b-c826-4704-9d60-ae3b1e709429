#include "system_info.h"
#include "usb_manager.h"
#include "config.h"

// Global instance
SystemInfo systemInfo;

SystemInfo::SystemInfo() : bootTime(0) {
}

void SystemInfo::begin() {
    bootTime = millis();
    debugPrint("System info initialized");
}

void SystemInfo::printSystemInfo() {
    debugPrint("=== OCTANE ESP32-S2 HID MOUSE SYSTEM INFO ===");
    debugPrint("Firmware Version: " + String(FIRMWARE_VERSION));
    debugPrint("Hardware Version: " + String(HARDWARE_VERSION));
    debugPrint("Chip Model: " + getChipModel());
    debugPrint("CPU Frequency: " + String(getCpuFreqMHz()) + " MHz");
    debugPrint("Free Heap: " + String(getFreeHeap()) + " bytes");
    debugPrint("Uptime: " + String(getUptime()) + " seconds");
    debugPrint("Reset Reason: " + getResetReason());
    debugPrint("===========================================");
}

uint32_t SystemInfo::getUptime() const {
    return (millis() - bootTime) / 1000;
}

size_t SystemInfo::getFreeHeap() const {
    return ESP.getFreeHeap();
}

String SystemInfo::getChipModel() const {
    return String(ESP.getChipModel());
}

uint32_t SystemInfo::getCpuFreqMHz() const {
    return ESP.getCpuFreqMHz();
}

String SystemInfo::getResetReason() const {
    esp_reset_reason_t reason = esp_reset_reason();
    switch (reason) {
        case ESP_RST_POWERON: return "Power-on reset";
        case ESP_RST_EXT: return "External reset";
        case ESP_RST_SW: return "Software reset";
        case ESP_RST_PANIC: return "Exception/panic reset";
        case ESP_RST_INT_WDT: return "Interrupt watchdog reset";
        case ESP_RST_TASK_WDT: return "Task watchdog reset";
        case ESP_RST_WDT: return "Other watchdog reset";
        case ESP_RST_DEEPSLEEP: return "Deep sleep reset";
        case ESP_RST_BROWNOUT: return "Brownout reset";
        case ESP_RST_SDIO: return "SDIO reset";
        default: return "Unknown reset";
    }
}

void SystemInfo::sendBootSequence() {
    // Send initial startup message
    usbManager.sendDebugMessage("STARTUP|" + String(FIRMWARE_VERSION) + "|" + 
                               getChipModel() + "|" + String(getCpuFreqMHz()) + "MHz");
    
    usbManager.sendDebugMessage("STATUS|READY|" + String(millis()) + "|" + String(getFreeHeap()));
    
    // Send periodic startup messages for first 30 seconds
    for (int i = 0; i < BOOT_STATUS_COUNT; i++) {
        delay(BOOT_STATUS_INTERVAL);
        usbManager.sendDebugMessage("BOOT_STATUS|" + String(i+1) + "|" + String(millis()) +
                                   "|" + String(usbManager.isReady() ? "USB_OK" : "USB_ERR"));
    }
}

void SystemInfo::debugPrint(const String& message) {
    usbManager.sendDebugMessage(message);
}
