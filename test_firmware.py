#!/usr/bin/env python3
"""
ESP32-S2 HID Mouse Firmware Test Script with F3 Key Control
Tests communication and functionality of the Octane firmware
Press F3 to move mouse, ESC to quit
"""

import serial
import json
import time
import sys
import threading
from typing import Optional

try:
    import keyboard
    KEYBOARD_AVAILABLE = True
except ImportError:
    print("⚠️ 'keyboard' module not found. Install with: pip install keyboard")
    print("   F3 key detection will not work without this module.")
    KEYBOARD_AVAILABLE = False

class ESP32Tester:
    def __init__(self, port: str = None, baud: int = 115200):
        self.port = port
        self.baud = baud
        self.serial_conn: Optional[serial.Serial] = None
        self.connected = False
    
    def find_esp32_port(self) -> Optional[str]:
        """Auto-detect ESP32 device by scanning COM ports with enhanced detection"""
        import serial.tools.list_ports

        print("Scanning for ESP32 devices...")
        ports = serial.tools.list_ports.comports()

        for port in ports:
            try:
                print(f"Testing port {port.device}...")
                test_serial = serial.Serial(port.device, self.baud, timeout=2)
                time.sleep(0.2)

                # Clear any existing data
                test_serial.flushInput()
                test_serial.flushOutput()

                # Try enhanced identify command first
                test_serial.write(b"identify\n")
                time.sleep(0.5)

                response = ""
                start_time = time.time()
                while time.time() - start_time < 2:
                    if test_serial.in_waiting > 0:
                        response += test_serial.read(test_serial.in_waiting).decode('utf-8', errors='ignore')
                    time.sleep(0.1)

                # Check for enhanced ESP32 response
                if "OCTANE_ESP32_ID:" in response:
                    print(f"✅ Found Enhanced ESP32 on {port.device}")
                    # Extract device ID
                    lines = response.split('\n')
                    device_id = ""
                    for line in lines:
                        if line.startswith("OCTANE_ESP32_ID:"):
                            device_id = line.replace("OCTANE_ESP32_ID:", "").strip()
                            break
                    print(f"   Device ID: {device_id}")
                    test_serial.close()
                    return port.device

                # Fallback to simple ping
                test_serial.write(b"PING\n")
                time.sleep(0.3)

                ping_response = ""
                start_time = time.time()
                while time.time() - start_time < 1:
                    if test_serial.in_waiting > 0:
                        ping_response += test_serial.read(test_serial.in_waiting).decode('utf-8', errors='ignore')
                    time.sleep(0.1)

                test_serial.close()

                if "PONG_ESP32_OCTANE" in ping_response:
                    print(f"✅ Found ESP32 on {port.device}: {ping_response.strip()}")
                    return port.device
                elif "ESP32" in ping_response and "Octane" in ping_response:
                    print(f"✅ Found ESP32 on {port.device}: {ping_response.strip()}")
                    return port.device

            except Exception as e:
                print(f"Error testing {port.device}: {e}")
                continue

        return None
    
    def connect(self) -> bool:
        """Connect to ESP32 device"""
        if not self.port:
            self.port = self.find_esp32_port()
            if not self.port:
                print("ERROR: No ESP32 device found!")
                return False
        
        try:
            self.serial_conn = serial.Serial(self.port, self.baud, timeout=2)
            time.sleep(1)  # Wait for connection to stabilize
            self.connected = True
            print(f"Connected to ESP32 on {self.port}")
            return True
        except Exception as e:
            print(f"ERROR: Failed to connect to {self.port}: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from ESP32"""
        if self.serial_conn:
            self.serial_conn.close()
            self.connected = False
            print("Disconnected from ESP32")
    
    def send_command(self, command: str) -> str:
        """Send command and get response"""
        if not self.connected:
            return "ERROR: Not connected"
        
        try:
            self.serial_conn.write((command + "\n").encode())
            response = self.serial_conn.readline().decode().strip()
            return response
        except Exception as e:
            return f"ERROR: {e}"
    
    def send_json_command(self, cmd_type: str, data: dict = None) -> dict:
        """Send JSON command and parse JSON response"""
        if data is None:
            data = {}
        
        command = {
            "Type": cmd_type,
            "Data": data
        }
        
        command_str = json.dumps(command)
        response_str = self.send_command(command_str)
        
        try:
            return json.loads(response_str)
        except json.JSONDecodeError:
            return {"status": "error", "message": f"Invalid JSON response: {response_str}"}
    
    def test_device_identification(self) -> bool:
        """Test enhanced device identification"""
        print("\n=== Testing Device Identification ===")

        # Test identify command
        response = self.send_command("identify")
        print(f"Identify response: {response}")
        success1 = "OCTANE_ESP32_ID:" in response

        # Test get_device_info command
        response = self.send_command("get_device_info")
        print(f"Device info response: {response}")
        success2 = "device_id" in response and "{" in response

        return success1 or success2

    def test_ping(self) -> bool:
        """Test ping functionality"""
        print("\n=== Testing Ping ===")

        # Test simple ping
        response = self.send_command("PING")
        print(f"Simple ping: {response}")
        success1 = "PONG" in response and "ESP32" in response

        # Test JSON ping
        response = self.send_json_command("ping")
        print(f"JSON ping: {response}")
        success2 = response.get("status") == "pong"

        return success1 and success2
    
    def test_status(self) -> bool:
        """Test status reporting"""
        print("\n=== Testing Status ===")
        
        # Test simple status
        response = self.send_command("STATUS")
        print(f"Simple status: {response}")
        success1 = "STATUS:OK" in response
        
        # Test JSON status
        response = self.send_json_command("status")
        print(f"JSON status: {json.dumps(response, indent=2)}")
        success2 = response.get("status") == "connected"
        
        return success1 and success2
    
    def test_mouse_movement(self) -> bool:
        """Test mouse movement commands"""
        print("\n=== Testing Mouse Movement ===")
        
        # Test simple movement
        response = self.send_command("M10,5")
        print(f"Simple movement (M10,5): {response}")
        success1 = "MOVED" in response
        
        # Test JSON movement
        response = self.send_json_command("mouse_move", {"x": -10, "y": -5})
        print(f"JSON movement: {response}")
        success2 = response.get("status") == "moved"
        
        return success1 and success2
    
    def test_mouse_clicks(self) -> bool:
        """Test mouse click commands"""
        print("\n=== Testing Mouse Clicks ===")
        
        # Test simple clicks
        response1 = self.send_command("CLICK_LEFT_DOWN")
        print(f"Left click down: {response1}")
        time.sleep(0.1)
        
        response2 = self.send_command("CLICK_LEFT_UP")
        print(f"Left click up: {response2}")
        
        # Test JSON clicks
        response3 = self.send_json_command("mouse_click", {"button": "right", "action": "down"})
        print(f"JSON right click down: {response3}")
        time.sleep(0.1)
        
        response4 = self.send_json_command("mouse_click", {"button": "right", "action": "up"})
        print(f"JSON right click up: {response4}")
        
        return all("CLICKED" in r or "RELEASED" in r or r.get("status") == "clicked" 
                  for r in [response1, response2, response3, response4])
    
    def test_queue_status(self) -> bool:
        """Test command queue status"""
        print("\n=== Testing Queue Status ===")
        
        response = self.send_json_command("queue_status")
        print(f"Queue status: {json.dumps(response, indent=2)}")
        
        return response.get("status") == "ok" and "queue_size" in response.get("data", {})
    
    def test_performance(self) -> bool:
        """Test performance with multiple commands"""
        print("\n=== Testing Performance ===")
        
        start_time = time.time()
        commands_sent = 0
        
        # Send 50 movement commands rapidly
        for i in range(50):
            x = (i % 10) - 5
            y = (i % 8) - 4
            response = self.send_json_command("mouse_move", {"x": x, "y": y})
            if response.get("status") == "moved":
                commands_sent += 1
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"Sent {commands_sent}/50 commands in {duration:.2f}s")
        print(f"Average: {duration/commands_sent*1000:.1f}ms per command")
        
        return commands_sent >= 45  # Allow some failures
    
    def run_all_tests(self) -> bool:
        """Run all tests and return overall success"""
        if not self.connect():
            return False
        
        tests = [
            ("Device Identification Test", self.test_device_identification),
            ("Ping Test", self.test_ping),
            ("Status Test", self.test_status),
            ("Mouse Movement Test", self.test_mouse_movement),
            ("Mouse Click Test", self.test_mouse_clicks),
            ("Queue Status Test", self.test_queue_status),
            ("Performance Test", self.test_performance)
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                result = test_func()
                results.append(result)
                status = "PASS" if result else "FAIL"
                print(f"\n{test_name}: {status}")
            except Exception as e:
                print(f"\n{test_name}: ERROR - {e}")
                results.append(False)
        
        self.disconnect()
        
        # Print summary
        passed = sum(results)
        total = len(results)
        print(f"\n{'='*50}")
        print(f"TEST SUMMARY: {passed}/{total} tests passed")
        print(f"{'='*50}")
        
        return passed == total

def main():
    """Main test function"""
    print("ESP32-S2 HID Mouse Firmware Test")
    print("=" * 40)
    
    # Check for port argument
    port = None
    if len(sys.argv) > 1:
        port = sys.argv[1]
        print(f"Using specified port: {port}")
    
    # Create tester and run tests
    tester = ESP32Tester(port)
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ All tests passed! Firmware is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Check firmware and connections.")
        sys.exit(1)

if __name__ == "__main__":
    main()
