#ifndef COMMAND_PROCESSOR_H
#define COMMAND_PROCESSOR_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <freertos/FreeRTOS.h>
#include <freertos/queue.h>
#include "config.h"

// ===== COMMAND PROCESSOR CLASS =====
class CommandProcessor {
private:
    QueueHandle_t commandQueue;
    String commandBuffer;
    uint32_t commandsProcessed;
    uint32_t lastHeartbeat;

    // NEW: Weapon-based recoil state
    int currentWeaponId;
    int currentBullet;
    bool recoilActive;
    float currentSensitivity;
    int currentSmoothing;

public:
    CommandProcessor();
    void begin();
    void processSerialCommands();
    void processCommandQueue();
    void sendHeartbeat();
    uint32_t getCommandsProcessed() const;
    void executeSmoothRecoil(float totalX, float totalY, int steps, int delayPerStep);
    void executeInterpolatedRecoil(float fromX, float fromY, float toX, float toY, int steps, int totalTime);
    void executeRecoilSequence(const String& params);
    void executeSmartBufferedBullet(const String& params);

    // NEW: Weapon-based recoil system
    void executeWeaponStart(const String& params);
    void executeWeaponStop();
    void executeWeaponBullet();

private:
    bool parseCommand(const String& command);
    bool parseJSONCommand(const String& jsonStr);
    bool parseSimpleCommand(const String& command);
    bool queueCommand(const HIDCommand& cmd);
    void executeCommand(const HIDCommand& cmd);
    void sendStatusResponse();
    void sendSystemInfo();
};

// Global command processor instance
extern CommandProcessor commandProcessor;

#endif // COMMAND_PROCESSOR_H
