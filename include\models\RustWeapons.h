#pragma once

#include <QObject>
#include <QString>
#include <QList>
#include <QJsonObject>
#include <QJsonArray>
#include <memory>
#include "Weapon.h"
#include "Vector2.h"

namespace octane {
namespace models {

/**
 * @brief Rust-specific weapon data and recoil patterns
 * 
 * This class contains all the weapon data specific to the game Rust,
 * including recoil patterns, weapon statistics, and attachment configurations.
 */
class RustWeapons : public QObject
{
    Q_OBJECT

public:
    explicit RustWeapons(QObject* parent = nullptr);
    ~RustWeapons();

    /**
     * @brief Initialize weapon data
     */
    bool initialize();

    /**
     * @brief Get all available weapons
     */
    QList<std::shared_ptr<Weapon>> getAllWeapons() const;

    /**
     * @brief Get weapon by name
     */
    std::shared_ptr<Weapon> getWeapon(const QString& name) const;

    /**
     * @brief Get weapons by category
     */
    QList<std::shared_ptr<Weapon>> getWeaponsByCategory(const QString& category) const;

    /**
     * @brief Get all weapon categories
     */
    QStringList getCategories() const;

    /**
     * @brief Get weapon names
     */
    QStringList getWeaponNames() const;

    /**
     * @brief Check if weapon exists
     */
    bool hasWeapon(const QString& name) const;

    /**
     * @brief Load weapon data from JSON
     */
    bool loadFromJson(const QJsonObject& jsonData);

    /**
     * @brief Save weapon data to JSON
     */
    QJsonObject saveToJson() const;

    /**
     * @brief Create default weapon set
     */
    void createDefaultWeapons();

signals:
    /**
     * @brief Emitted when weapon data is loaded
     */
    void weaponsLoaded();

    /**
     * @brief Emitted when weapon data changes
     */
    void weaponsChanged();

private:
    void createAK47();
    void createM4A4();
    void createM4A1S();
    void createAWP();
    void createAUG();
    void createSG553();
    void createFAMAS();
    void createGalilAR();
    void createMP5();
    void createUMP45();
    void createP90();
    void createBizon();
    void createMAC10();
    void createMP9();
    void createMP7();
    void createNova();
    void createXM1014();
    void createSawedOff();
    void createMAG7();
    void createM249();
    void createNegev();
    void createGlock18();
    void createUSPS();
    void createP2000();
    void createP250();
    void createFiveSeven();
    void createCZ75Auto();
    void createTec9();
    void createDualBerettas();
    void createDeagle();
    void createRevolver();

    std::shared_ptr<Weapon> createWeapon(const QString& name, const QString& category);
    void addRecoilPattern(std::shared_ptr<Weapon> weapon, const QList<Vector2>& pattern);
    void addAdsRecoilPattern(std::shared_ptr<Weapon> weapon, const QList<Vector2>& pattern);
    void setupWeaponAttachments(std::shared_ptr<Weapon> weapon);

private:
    QList<std::shared_ptr<Weapon>> m_weapons;
    bool m_initialized;
};

} // namespace models
} // namespace octane
