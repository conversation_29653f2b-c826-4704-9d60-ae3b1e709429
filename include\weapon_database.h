#pragma once

#include <Arduino.h>
#include "config.h"

struct RecoilPoint {
    float x;
    float y;
};

struct WeaponData {
    int weaponId;
    String name;
    int bulletDelay;        // Time between bullets in ms
    int maxBullets;         // Maximum bullets in pattern
    RecoilPoint pattern[MAX_RECOIL_POINTS];
};

class WeaponDatabase {
public:
    WeaponDatabase();
    
    // Initialize with all weapon data
    void initialize();
    
    // Get weapon data by ID
    const WeaponData* getWeapon(int weaponId) const;
    
    // Get weapon data by name
    const WeaponData* getWeapon(const String& weaponName) const;
    
    // Check if weapon exists
    bool hasWeapon(int weaponId) const;
    
    // Get total number of weapons
    int getWeaponCount() const;
    
    // Debug: Print all weapons
    void printWeapons() const;

private:
    WeaponData weapons[MAX_WEAPONS];
    int weaponCount;
    
    // Helper to add weapon
    void addWeapon(int id, const String& name, int delay, const float recoilData[][2], int bulletCount);
};

extern WeaponDatabase weaponDB;
