#!/usr/bin/env python3
"""
Calculate the most accurate AK recoil pattern by averaging multiple sources
"""

import json

# Pattern 1 & 2: Traditional template pattern (first 30 bullets)
pattern_traditional = [
    (0.000000, -2.257792), (0.323242, -2.300758), (0.649593, -2.299759), (0.848786, -2.259034),
    (1.075408, -2.323947), (1.268491, -2.215956), (1.330963, -2.236556), (1.336833, -2.218203),
    (1.505516, -2.143454), (1.504423, -2.233091), (1.442116, -2.270194), (1.478543, -2.204318),
    (1.392874, -2.165817), (1.480824, -2.177887), (1.597069, -2.270915), (1.449996, -2.145893),
    (1.369179, -2.270450), (1.582363, -2.298334), (1.516872, -2.235066), (1.498249, -2.238401),
    (1.465769, -2.331642), (1.564812, -2.242621), (1.517519, -2.303052), (1.422433, -2.211946),
    (1.553195, -2.248043), (1.510463, -2.285327), (1.553878, -2.240047), (1.520380, -2.221839),
    (1.553878, -2.240047), (1.553195, -2.248043)
]

# Pattern 3: JSON curve data (converted to traditional format)
# The JSON uses curve-x (horizontal) and curve-y (vertical) arrays
curve_x = [0.0, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5]
curve_y = [-0.0, -0.2666199883647539, -0.5209992038497219, -0.7633411283642054, -0.9938492438175061, -1.2127270321189252, -1.420177975177765, -1.6164055549033263, -1.8016132532049107, -1.9760045519918201, -2.139782933173356, -2.2931518786588203, -2.436314870357513, -2.569475390178738, -2.692836920031795, -2.806602941825986, -2.910976937470613, -3.006162388874977, -3.0923627779483795, -3.1697815866001227, -3.2386222967395075, -3.2990883902758363, -3.351383349118409, -3.3957106551765293, -3.4322737903594964, -3.4612762365766145, -3.4829214757371836, -3.4974129897505044, -3.50495426052588, -3.5057487699726115]

# Convert JSON curve data to traditional format
# Note: JSON curves seem to be cumulative, need to convert to per-bullet deltas
pattern_json = []
for i in range(30):
    if i == 0:
        # First bullet
        x_delta = curve_x[i]
        y_delta = curve_y[i]
    else:
        # Calculate delta from previous position
        x_delta = curve_x[i] - curve_x[i-1]
        y_delta = curve_y[i] - curve_y[i-1]
    
    pattern_json.append((x_delta, y_delta))

print("=== AK RECOIL PATTERN ANALYSIS ===")
print()

print("Pattern 1 (Traditional Template):")
for i, (x, y) in enumerate(pattern_traditional[:10]):
    print(f"  Bullet {i+1:2d}: ({x:8.6f}, {y:8.6f})")
print("  ...")

print()
print("Pattern 2 (JSON Curves - converted to deltas):")
for i, (x, y) in enumerate(pattern_json[:10]):
    print(f"  Bullet {i+1:2d}: ({x:8.6f}, {y:8.6f})")
print("  ...")

print()
print("=== CALCULATING AVERAGED PATTERN ===")

# Calculate averaged pattern
averaged_pattern = []
for i in range(30):
    # Average the X and Y values from both patterns
    avg_x = (pattern_traditional[i][0] + pattern_json[i][0]) / 2.0
    avg_y = (pattern_traditional[i][1] + pattern_json[i][1]) / 2.0
    averaged_pattern.append((avg_x, avg_y))

print()
print("Averaged AK Pattern (30 bullets):")
for i, (x, y) in enumerate(averaged_pattern):
    print(f"  Bullet {i+1:2d}: ({x:9.6f}, {y:9.6f})")

print()
print("=== C++ CODE FOR WeaponData.cpp ===")
print()
print("std::vector<Vector2> recoilPattern = {")
for i, (x, y) in enumerate(averaged_pattern):
    if i == len(averaged_pattern) - 1:
        print(f"    Vector2({x:9.6f}f, {y:9.6f}f)")
    else:
        print(f"    Vector2({x:9.6f}f, {y:9.6f}f),")
print("};")

print()
print("=== COMPARISON ANALYSIS ===")
print()
print("Differences between patterns:")
for i in range(min(10, len(pattern_traditional))):
    trad_x, trad_y = pattern_traditional[i]
    json_x, json_y = pattern_json[i]
    diff_x = abs(trad_x - json_x)
    diff_y = abs(trad_y - json_y)
    print(f"  Bullet {i+1:2d}: X diff={diff_x:6.3f}, Y diff={diff_y:6.3f}")

print()
print("=== TIMING INFORMATION ===")
print("Delay: 133.3ms (from JSON data)")
print("Magazine: 30 bullets")
print("Automatic: Yes")
print("Hip fire scale: 0.83")
