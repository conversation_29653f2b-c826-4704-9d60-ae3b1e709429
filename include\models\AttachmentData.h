#pragma once
#include <vector>
#include <string>
#include "Attachment.h"

namespace octane {
namespace models {

/**
 * @brief Contains hardcoded attachment data from latest Rust dumps
 */
class AttachmentData {
public:
    /**
     * @brief Get all available attachments
     */
    static std::vector<Attachment> getAllAttachments();

    /**
     * @brief Get an attachment by its ID
     */
    static Attachment getAttachmentById(int id);

    /**
     * @brief Get an attachment by its name
     */
    static Attachment getAttachmentByName(const std::string& name);

    /**
     * @brief Get attachment names for UI dropdown
     */
    static std::vector<std::string> getAttachmentNames();

    /**
     * @brief Get scope attachments only
     */
    static std::vector<Attachment> getScopeAttachments();

    /**
     * @brief Get muzzle attachments only
     */
    static std::vector<Attachment> getMuzzleAttachments();

    /**
     * @brief Get other attachments (flashlight, laser, etc.)
     */
    static std::vector<Attachment> getOtherAttachments();

private:
    // Attachment creation methods - Scopes
    static Attachment createHoloSight();
    static Attachment createSimpleSight();
    static Attachment createSmallScope();
    static Attachment create8xScope();
    
    // Muzzle attachments
    static Attachment createSilencer();
    static Attachment createMuzzleBrake();
    static Attachment createMuzzleBoost();
    
    // Other attachments
    static Attachment createFlashlight();
    static Attachment createLaserSight();
    static Attachment createExtendedMags();

    // Static attachment instances - Scopes
    static const Attachment HOLO_SIGHT;
    static const Attachment SIMPLE_SIGHT;
    static const Attachment SMALL_SCOPE;
    static const Attachment SCOPE_8X;
    
    // Muzzle attachments
    static const Attachment SILENCER;
    static const Attachment MUZZLE_BRAKE;
    static const Attachment MUZZLE_BOOST;
    
    // Other attachments
    static const Attachment FLASHLIGHT;
    static const Attachment LASER_SIGHT;
    static const Attachment EXTENDED_MAGS;
};

} // namespace models
} // namespace octane
