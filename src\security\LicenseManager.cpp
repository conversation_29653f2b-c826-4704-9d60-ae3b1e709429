#include "security/LicenseManager.h"
#include <iostream>
#include <sstream>
#include <iomanip>

namespace octane {
namespace security {

// Static member definitions
LicenseManager::LicenseInfo LicenseManager::s_currentLicense;
std::string LicenseManager::s_currentKey;

LicenseManager::LicenseInfo LicenseManager::validateKey(const std::string& key) {
    LicenseInfo info;

    if (key.empty()) {
        return info; // Invalid by default
    }

    // Accept any non-empty key (VPS validation already happened)
    info.isValid = true;

    // For now, simulate license info based on key
    // In production, this info would come from the VPS response

    if (key == "TEST" || key == "test" || key == "TEST123" || key == "TESTKEY") {
        info.keyType = "Test License";
        info.expirationDate = std::chrono::system_clock::now() + std::chrono::hours(24 * 7); // 7 days
        info.daysRemaining = 7;
        info.userName = "TestUser";
    } else {
        // Default to a reasonable license for any other valid key
        info.keyType = "Premium License";
        info.expirationDate = std::chrono::system_clock::now() + std::chrono::hours(24 * 365); // 1 year
        auto now = std::chrono::system_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::hours>(info.expirationDate - now);
        info.daysRemaining = static_cast<int>(duration.count() / 24);

        // Extract username from key
        if (key.length() >= 4) {
            info.userName = "User_" + key.substr(0, 4);
        } else {
            info.userName = "User_" + key;
        }
    }

    return info;
}

bool LicenseManager::isLicenseValid() {
    if (!s_currentLicense.isValid) {
        return false;
    }
    
    auto now = std::chrono::system_clock::now();
    return now < s_currentLicense.expirationDate;
}

LicenseManager::LicenseInfo LicenseManager::getCurrentLicense() {
    // Update days remaining
    if (s_currentLicense.isValid) {
        auto now = std::chrono::system_clock::now();
        if (s_currentLicense.expirationDate != std::chrono::system_clock::time_point::max()) {
            auto duration = std::chrono::duration_cast<std::chrono::hours>(s_currentLicense.expirationDate - now);
            s_currentLicense.daysRemaining = std::max(0, static_cast<int>(duration.count() / 24));
        }
    }
    
    return s_currentLicense;
}

bool LicenseManager::setLicenseKey(const std::string& key) {
    auto info = validateKey(key);
    if (info.isValid) {
        s_currentLicense = info;
        s_currentKey = key;
        std::cout << "License key accepted: " << info.keyType << " (" << info.daysRemaining << " days remaining)" << std::endl;
        return true;
    }
    
    std::cout << "Invalid license key provided" << std::endl;
    return false;
}

} // namespace security
} // namespace octane
