/*
 * ESP32-S2 HID Mouse Firmware - Octane Recoil Control System
 * Version: 2.1.0 - Refactored Architecture
 * Target: ESP32-S2 Mini (Lolin S2 Mini)
 *
 * Features:
 * - USB HID Mouse emulation with CDC Serial
 * - Modular architecture with separate components
 * - Enhanced debug output and status reporting
 * - LED status indication system
 * - Command processing with queue system
 * - System information and monitoring
 */

#include <Arduino.h>
#include "config.h"
#include "led_controller.h"
#include "usb_manager.h"
#include "command_processor.h"
#include "system_info.h"

// External USB Serial reference
extern USBCDC USBSerial;

// ===== SETUP FUNCTION =====
void setup() {
    // Initialize USB serial communication (will be handled by USB manager)
    
    // Initialize all components
    systemInfo.begin();
    ledController.begin();
    usbManager.begin();
    commandProcessor.begin();
    
    // Print system information
    systemInfo.printSystemInfo();
    
    // Set initial LED state
    ledController.setState(LED_WAITING);
    
    // Send boot sequence messages
    systemInfo.sendBootSequence();
    
    // Set ready state
    ledController.setState(LED_CONNECTED);
    
    USBSerial.println("Octane ESP32-S2 HID Mouse v" + String(FIRMWARE_VERSION) + " Ready");
}

// ===== MAIN LOOP =====
void loop() {
    // Process serial commands
    commandProcessor.processSerialCommands();
    
    // Process command queue
    commandProcessor.processCommandQueue();
    
    // Update LED pattern
    ledController.update();
    
    // Send heartbeat messages
    commandProcessor.sendHeartbeat();
    
    // Small delay to prevent watchdog issues
    delay(1);
}
