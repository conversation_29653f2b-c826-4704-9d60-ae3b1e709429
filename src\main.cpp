#include <windows.h>
#include <iostream>

#include "core/Application.h"
#include "views/MainWindow.h"

using namespace octane;

// Main code
int main()
{
    std::cout << "Starting Octane Recoil Controller v4.2.1..." << std::endl;

    // Initialize COM for WebView2
    HRESULT hr = CoInitializeEx(nullptr, COINIT_APARTMENTTHREADED);
    if (FAILED(hr)) {
        std::cerr << "Failed to initialize COM. HRESULT: 0x" << std::hex << hr << std::endl;
        return 1;
    }
    std::cout << "COM initialized successfully" << std::endl;

    // Initialize Octane Application
    core::Application app;
    if (!app.initialize()) {
        std::cerr << "Failed to initialize Octane Application" << std::endl;
        CoUninitialize();
        return 1;
    }

    // Create main window
    views::MainWindow mainWindow(&app);
    if (!mainWindow.initialize()) {
        std::cerr << "Failed to initialize main window" << std::endl;
        app.shutdown();
        CoUninitialize();
        return 1;
    }

    // Show the window
    mainWindow.show();

    // Main message loop
    std::cout << "Application running with embedded WebView2..." << std::endl;
    std::cout << "Close the window to exit the application." << std::endl;

    while (!mainWindow.shouldClose()) {
        mainWindow.processMessages();
        Sleep(16); // ~60 FPS
    }

    std::cout << "Application shutting down..." << std::endl;

    // Cleanup
    mainWindow.shutdown();
    app.shutdown();
    CoUninitialize();

    return 0;
}
