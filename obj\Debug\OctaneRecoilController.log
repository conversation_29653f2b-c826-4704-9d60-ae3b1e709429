﻿  main.cpp
  Application.cpp
C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\include\services\AuthenticationService.h(12,64): warning C4100: 'key': unreferenced formal parameter
  (compiling source file 'src/core/Application.cpp')
  
  AppConfig.cpp
  Logger.cpp
  Attachment.cpp
  AttachmentData.cpp
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\algorithm(3769,24): warning C4244: '=': conversion from 'int' to 'char', possible loss of data
  (compiling source file 'src/models/AttachmentData.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\algorithm(3769,24):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\src\models\AttachmentData.cpp(47,36):
          see reference to function template instantiation '_OutIt std::transform<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,int(__cdecl *)(int)>(const _InIt,const _InIt,_OutIt,_Fn)' being compiled
          with
          [
              _OutIt=std::_String_iterator<std::_String_val<std::_Simple_types<char>>>,
              _Elem=char,
              _InIt=std::_String_iterator<std::_String_val<std::_Simple_types<char>>>,
              _Fn=int (__cdecl *)(int)
          ]
  
  KeybindSettings.cpp
C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\src\models\KeybindSettings.cpp(118,16): warning C4244: 'initializing': conversion from 'int' to 'char', possible loss of data
  RecoilSettings.cpp
C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\include\models\RecoilSettings.h(72,42): warning C4100: 'isADS': unreferenced formal parameter
  (compiling source file 'src/models/RecoilSettings.cpp')
  
  Vector2.cpp
  Weapon.cpp
  WeaponData.cpp
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\algorithm(3769,24): warning C4244: '=': conversion from 'int' to 'char', possible loss of data
  (compiling source file 'src/models/WeaponData.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\algorithm(3769,24):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\src\models\WeaponData.cpp(72,36):
          see reference to function template instantiation '_OutIt std::transform<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,int(__cdecl *)(int)>(const _InIt,const _InIt,_OutIt,_Fn)' being compiled
          with
          [
              _OutIt=std::_String_iterator<std::_String_val<std::_Simple_types<char>>>,
              _Elem=char,
              _InIt=std::_String_iterator<std::_String_val<std::_Simple_types<char>>>,
              _Fn=int (__cdecl *)(int)
          ]
  
  AdvancedSecurityManager.cpp
  LicenseManager.cpp
  AuthenticationService.cpp
C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\include\services\AuthenticationService.h(12,64): warning C4100: 'key': unreferenced formal parameter
  (compiling source file 'src/services/AuthenticationService.cpp')
  
  ConfigurationService.cpp
  DeviceService.cpp
  ESP32Service.cpp
  RecoilControlService.cpp
C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\include\models\RecoilSettings.h(72,42): warning C4100: 'isADS': unreferenced formal parameter
  (compiling source file 'src/services/RecoilControlService.cpp')
  
C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\src\services\RecoilControlService.cpp(600,16): warning C4244: 'initializing': conversion from 'int' to 'char', possible loss of data
  SecurityService.cpp
C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\src\services\SecurityService.cpp(236,105): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\src\services\SecurityService.cpp(236,61): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\algorithm(3769,24): warning C4244: '=': conversion from 'int' to 'char', possible loss of data
  (compiling source file 'src/services/SecurityService.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\algorithm(3769,24):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\src\services\SecurityService.cpp(302,18):
          see reference to function template instantiation '_OutIt std::transform<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,int(__cdecl *)(int)>(const _InIt,const _InIt,_OutIt,_Fn)' being compiled
          with
          [
              _OutIt=std::_String_iterator<std::_String_val<std::_Simple_types<char>>>,
              _Elem=char,
              _InIt=std::_String_iterator<std::_String_val<std::_Simple_types<char>>>,
              _Fn=int (__cdecl *)(int)
          ]
  
  SoundService.cpp
  Generating Code...
  Compiling...
  ConfigManager.cpp
C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\include\models\RecoilSettings.h(72,42): warning C4100: 'isADS': unreferenced formal parameter
  (compiling source file 'src/utils/ConfigManager.cpp')
  
C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\src\utils\ConfigManager.cpp(262,40): warning C4101: 'e': unreferenced local variable
  MainWindow.cpp
C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\include\models\RecoilSettings.h(72,42): warning C4100: 'isADS': unreferenced formal parameter
  (compiling source file 'src/views/MainWindow.cpp')
  
C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\src\views\MainWindow.cpp(255,67): warning C4100: 'sender': unreferenced formal parameter
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xutility(4829,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'src/views/MainWindow.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xutility(4829,18):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\src\views\MainWindow.cpp(271,68):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\src\views\MainWindow.cpp(271,68):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'octane::views::MainWindow::initializeWebView::<lambda_1>::()::<lambda_1>::()::<lambda_1>::operator ()'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xstring(788,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xstring(944,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  MainWindowMessageHandlers.cpp
C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\include\models\RecoilSettings.h(72,42): warning C4100: 'isADS': unreferenced formal parameter
  (compiling source file 'src/views/MainWindowMessageHandlers.cpp')
  
  MainWindowHtml.cpp
  MainWindowScripts.cpp
  MainWindowStructure.cpp
  MainWindowStyles.cpp
  Generating Code...
  OctaneRecoilController.vcxproj -> C:\Users\<USER>\Desktop\recoil\desktop-app\desktop-appc++\bin\Debug\OctaneRecoilController.exe
