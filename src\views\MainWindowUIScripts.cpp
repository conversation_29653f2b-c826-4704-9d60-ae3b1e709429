#include "views/MainWindowUIScripts.h"

namespace octane {
namespace views {

std::string MainWindowUIScripts::getUIScripts()
{
    return R"(
<script>
    // ===== UI INTERACTION SCRIPTS =====
    
    // Tab switching functionality
    function showTab(tabName) {
        // Hide all tabs
        const tabs = document.querySelectorAll('.tab-content');
        tabs.forEach(tab => tab.style.display = 'none');
        
        // Remove active class from all tab buttons
        const tabButtons = document.querySelectorAll('.tab-button');
        tabButtons.forEach(button => button.classList.remove('active'));
        
        // Show selected tab
        const selectedTab = document.getElementById(tabName + '-tab');
        if (selectedTab) {
            selectedTab.style.display = 'block';
        }
        
        // Add active class to clicked button
        const selectedButton = document.querySelector(`[onclick="showTab('${tabName}')"]`);
        if (selectedButton) {
            selectedButton.classList.add('active');
        }
        
        currentTab = tabName;
    }

    // Weapon selection functions
    function onWeaponChange() {
        const weapon = document.getElementById('weapon').value;
        postMessageToNative({
            type: 'weaponChanged',
            weapon: weapon
        });
    }

    function onSightChange() {
        const sight = document.getElementById('sight').value;
        postMessageToNative({
            type: 'sightChanged',
            sight: sight
        });
    }

    function onMuzzleChange() {
        const muzzle = document.getElementById('muzzle').value;
        postMessageToNative({
            type: 'muzzleChanged',
            muzzle: muzzle
        });
    }

    // Sensitivity functions
    function onSensitivityChange() {
        const sensitivity = document.getElementById('sensitivity').value;
        const valueDisplay = document.getElementById('sensitivity-value');
        if (valueDisplay) {
            valueDisplay.textContent = sensitivity;
        }
        
        postMessageToNative({
            type: 'sensitivityChanged',
            value: parseFloat(sensitivity)
        });
    }

    function onAdsSensitivityChange() {
        const adsSensitivity = document.getElementById('ads-sensitivity').value;
        const valueDisplay = document.getElementById('ads-sensitivity-value');
        if (valueDisplay) {
            valueDisplay.textContent = adsSensitivity;
        }
        
        postMessageToNative({
            type: 'adsSensitivityChanged',
            value: parseFloat(adsSensitivity)
        });
    }

    function onFovChange() {
        const fov = document.getElementById('fov').value;
        const valueDisplay = document.getElementById('fov-value');
        if (valueDisplay) {
            valueDisplay.textContent = fov;
        }
        
        postMessageToNative({
            type: 'fovChanged',
            value: parseFloat(fov)
        });
    }

    function onHumanizationChange() {
        const input = document.getElementById('humanization');
        if (input) {
            const value = input.value;
            const valueDisplay = document.getElementById('humanization-value');
            if (valueDisplay) {
                valueDisplay.textContent = value + '%';
            }
            
            postMessageToNative({
                type: 'humanizationChanged',
                value: parseInt(value)
            });
        }
    }
</script>
)";
}

} // namespace views
} // namespace octane
