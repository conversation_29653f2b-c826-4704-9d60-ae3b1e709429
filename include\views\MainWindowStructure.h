#pragma once
#include <string>

namespace octane {
namespace views {

/**
 * @brief Contains the HTML structure for the WebView2 interface
 */
class MainWindowStructure {
public:
    /**
     * @brief Get the HTML structure for the main window body
     */
    static std::string getHtmlStructure();

private:
    static std::string getMainTab();
    static std::string getSettingsTab();
    static std::string getFeaturesTab();
    static std::string getKeybindsTab();
    static std::string getLoadoutsTab();
    static std::string getMiscellaneousTab();

    // Legacy methods for compatibility
    static std::string getMainTabContent();
    static std::string getSettingsTabContent();
    static std::string getMiscTabContent();
    static std::string getKeybindsTabContent();
    static std::string getLoadoutsTabContent();

    MainWindowStructure() = delete; // Static class
};

} // namespace views
} // namespace octane
