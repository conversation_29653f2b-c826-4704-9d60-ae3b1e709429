#include "models/WeaponData.h"
#include <algorithm>

namespace octane {
namespace models {

// Static weapon instances - Assault Rifles
const Weapon WeaponData::AK47U = WeaponData::createAK47U();
const Weapon WeaponData::LR300 = WeaponData::createLR300();
const Weapon WeaponData::M249 = WeaponData::createM249();
const Weapon WeaponData::SEMI_AUTO_RIFLE = WeaponData::createSemiAutoRifle();

// SMGs
const Weapon WeaponData::MP5 = WeaponData::createMP5();
const Weapon WeaponData::SMG = WeaponData::createSMG();
const Weapon WeaponData::THOMPSON = WeaponData::createThompson();
const Weapon WeaponData::HMLMG = WeaponData::createHMLMG();

// Pistols
const Weapon WeaponData::PYTHON = WeaponData::createPython();
const Weapon WeaponData::PISTOL_REVOLVER = WeaponData::createPistolRevolver();
const Weapon WeaponData::PISTOL_SEMIAUTO = WeaponData::createPistolSemiAuto();
const Weapon WeaponData::M92 = WeaponData::createM92();

// Attachment modifiers
const std::unordered_map<std::string, float> WeaponData::AttachmentModifiers::scopeModifiers = {
    {"none", 1.0f},
    {"holo", 0.98f},
    {"red-dot", 0.99f},
    {"acog", 0.95f},
    {"scope", 0.90f}
};

const std::unordered_map<std::string, float> WeaponData::AttachmentModifiers::muzzleModifiers = {
    {"none", 1.0f},
    {"compensator", 0.80f},
    {"flash-hider", 0.90f},
    {"muzzle-brake", 0.50f},
    {"silencer", 0.80f}
};

const std::unordered_map<std::string, float> WeaponData::AttachmentModifiers::barrelModifiers = {
    {"none", 1.0f},
    {"extended", 0.95f},
    {"heavy", 0.85f}
};

std::vector<Weapon> WeaponData::getAllWeapons() {
    return {
        // Assault Rifles
        AK47U, LR300, M249, SEMI_AUTO_RIFLE,
        // SMGs
        MP5, SMG, THOMPSON, HMLMG,
        // Pistols
        PYTHON, PISTOL_REVOLVER, PISTOL_SEMIAUTO, M92
    };
}

Weapon WeaponData::getWeaponById(int id) {
    auto weapons = getAllWeapons();
    auto it = std::find_if(weapons.begin(), weapons.end(), 
                          [id](const Weapon& w) { return w.getId() == id; });
    return (it != weapons.end()) ? *it : Weapon();
}

Weapon WeaponData::getWeaponByName(const std::string& name) {
    auto weapons = getAllWeapons();
    auto it = std::find_if(weapons.begin(), weapons.end(), 
                          [&name](const Weapon& w) { 
                              std::string weaponName = w.getName();
                              std::string searchName = name;
                              std::transform(weaponName.begin(), weaponName.end(), weaponName.begin(), ::tolower);
                              std::transform(searchName.begin(), searchName.end(), searchName.begin(), ::tolower);
                              return weaponName == searchName; 
                          });
    return (it != weapons.end()) ? *it : Weapon();
}

std::vector<std::string> WeaponData::getWeaponNames() {
    auto weapons = getAllWeapons();
    std::vector<std::string> names;
    names.reserve(weapons.size());
    for (const auto& weapon : weapons) {
        names.push_back(weapon.getName());
    }
    return names;
}

Weapon WeaponData::createAK47U() {
    // AK47U recoil pattern - RESTORED WORKING PATTERN (was better before)
    // This pattern was working better - focus on bullet sync, not pattern data for now
    std::vector<Vector2> recoilPattern = {
        Vector2(0.000000f, -2.257792f), Vector2(0.323242f, -2.300758f), Vector2(0.649593f, -2.299759f),
        Vector2(0.848786f, -2.259034f), Vector2(1.075408f, -2.323947f), Vector2(1.268491f, -2.215956f),
        Vector2(1.330963f, -2.236556f), Vector2(1.336833f, -2.218203f), Vector2(1.505516f, -2.143454f),
        Vector2(1.504423f, -2.233091f), Vector2(1.442116f, -2.270194f), Vector2(1.478543f, -2.204318f),
        Vector2(1.392874f, -2.165817f), Vector2(1.480824f, -2.177887f), Vector2(1.597069f, -2.270915f),
        Vector2(1.449996f, -2.145893f), Vector2(1.369179f, -2.270450f), Vector2(1.582363f, -2.298334f),
        Vector2(1.516872f, -2.235066f), Vector2(1.498249f, -2.238401f), Vector2(1.465769f, -2.331642f),
        Vector2(1.564812f, -2.242621f), Vector2(1.517519f, -2.303052f), Vector2(1.422433f, -2.211946f),
        Vector2(1.553195f, -2.248043f), Vector2(1.510463f, -2.285327f), Vector2(1.553878f, -2.240047f),
        Vector2(1.520380f, -2.221839f), Vector2(1.553878f, -2.240047f), Vector2(1.553195f, -2.248043f)
    };

    // Template weapon properties: delay=133.3ms (from Rust dump: 133.30000638961792), max_bullets=30, hipfire_scale=0.83f, is_automatic=true
    return Weapon("AK", 1, 133, 30, 0.83f, true, recoilPattern);
}

Weapon WeaponData::createLR300() {
    // LR300 recoil pattern from Rust dump data
    std::vector<Vector2> recoilPattern = {
        Vector2(0.0f, -0.0f),
        Vector2(1.0f, -0.8152826567572706f),
        Vector2(1.0f, -1.9187874008081354f),
        Vector2(1.0f, -2.2145613091958647f),
        Vector2(1.0f, -2.3007752118601834f),
        Vector2(1.0f, -2.380988391952822f),
        Vector2(1.0f, -2.455393641590543f),
        Vector2(1.0f, -2.5241837528901065f),
        Vector2(1.0f, -2.587551517968275f),
        Vector2(1.0f, -2.645689728941809f),
        Vector2(1.0f, -2.6987911779274705f),
        Vector2(1.0f, -2.7470486570420207f),
        Vector2(1.0f, -2.7906549584022207f),
        Vector2(1.0f, -2.829802874124833f),
        Vector2(1.0f, -2.8646851963266173f),
        Vector2(1.0f, -2.8954947171243366f),
        Vector2(1.0f, -2.9224242286347515f),
        Vector2(1.0f, -2.9456665229746237f),
        Vector2(1.0f, -2.9654143922607146f),
        Vector2(1.0f, -2.981860628609785f),
        Vector2(1.0f, -2.9951980241385967f),
        Vector2(1.0f, -3.005619370963911f),
        Vector2(1.0f, -3.01331746120249f),
        Vector2(1.0f, -3.018485086971095f),
        Vector2(1.0f, -3.0213150403864857f),
        Vector2(1.0f, -3.0220001135654253f),
        Vector2(1.0f, -3.0207330986246754f),
        Vector2(1.0f, -3.017706787680996f),
        Vector2(1.0f, -3.0131139728511496f),
        Vector2(1.0f, -3.0071474462518966f)
    };

    return Weapon("LR-300", 2, 120, 30, 0.75f, true, recoilPattern);
}

Weapon WeaponData::createMP5() {
    // MP5 recoil pattern from Rust dump data
    std::vector<Vector2> recoilPattern = {
        Vector2(0.0f, -0.0f),
        Vector2(1.0f, -2.997337200907195f),
        Vector2(1.0f, -3.0001864491075096f),
        Vector2(1.0f, -3.0248042128190376f),
        Vector2(1.0f, -3.1315891635409634f),
        Vector2(1.0f, -3.4038967679565797f),
        Vector2(1.0f, -3.7482576425701257f),
        Vector2(1.0f, -4.054264182576212f),
        Vector2(1.0f, -4.25617524648304f),
        Vector2(1.0f, -4.374178100090777f),
        Vector2(1.0f, -4.43208353552418f),
        Vector2(1.0f, -4.453702344908002f),
        Vector2(1.0f, -4.462845320366999f),
        Vector2(1.0f, -4.483323254025917f),
        Vector2(1.0f, -4.53894693800952f),
        Vector2(1.0f, -4.653527164442561f),
        Vector2(1.0f, -4.838257889078607f),
        Vector2(1.0f, -5.061200597853809f),
        Vector2(1.0f, -5.299906322909776f),
        Vector2(1.0f, -5.5331480465444685f),
        Vector2(1.0f, -5.739698751055839f),
        Vector2(1.0f, -5.898331418741837f),
        Vector2(1.0f, -5.987819031900402f),
        Vector2(1.0f, -6.0f),
        Vector2(1.0f, -6.0f),
        Vector2(1.0f, -6.0f),
        Vector2(1.0f, -6.0f),
        Vector2(1.0f, -6.0f),
        Vector2(1.0f, -6.0f),
        Vector2(1.0f, -6.0f)
    };

    return Weapon("MP5", 3, 100, 30, 0.5f, true, recoilPattern);
}

Weapon WeaponData::createM249() {
    // M249 recoil pattern from Rust dump data
    std::vector<Vector2> recoilPattern = {
        Vector2(0.0f, -0.0f),
        Vector2(1.0f, -0.24184755435179156f),
        Vector2(1.0f, -0.5624345174218754f),
        Vector2(1.0f, -0.9359260836247105f),
        Vector2(1.0f, -1.3381353965935063f),
        Vector2(1.0f, -1.7448755999614713f),
        Vector2(1.0f, -2.1319598373618143f),
        Vector2(1.0f, -2.4752012524277456f),
        Vector2(1.0f, -2.750412988792472f),
        Vector2(1.0f, -2.933408190089204f),
        Vector2(1.0f, -2.99999999995115f),
        Vector2(1.0f, -3.0003205635463317f),
        Vector2(1.0f, -3.0006268007291372f),
        Vector2(1.0f, -3.000918954320118f),
        Vector2(1.0f, -3.001197267139823f),
        Vector2(1.0f, -3.0014619820088004f),
        Vector2(1.0f, -3.001713341747599f),
        Vector2(1.0f, -3.001951589176767f),
        Vector2(1.0f, -3.002176967116853f),
        Vector2(1.0f, -3.0023897183884056f),
        Vector2(1.0f, -3.0025900858119723f),
        Vector2(1.0f, -3.002778312208103f),
        Vector2(1.0f, -3.0029546403973457f),
        Vector2(1.0f, -3.0031193132002487f),
        Vector2(1.0f, -3.0032725734373606f),
        Vector2(1.0f, -3.003414663929229f),
        Vector2(1.0f, -3.0035458274964046f),
        Vector2(1.0f, -3.003666306959434f),
        Vector2(1.0f, -3.0037763451388657f),
        Vector2(1.0f, -3.0038761848552493f),
        Vector2(1.0f, -3.0039660689291328f),
        Vector2(1.0f, -3.004046240181064f),
        Vector2(1.0f, -3.0041169414315925f),
        Vector2(1.0f, -3.0041784155012667f),
        Vector2(1.0f, -3.0042309052106333f),
        Vector2(1.0f, -3.004274653380244f),
        Vector2(1.0f, -3.004309902830644f),
        Vector2(1.0f, -3.0043368963823838f),
        Vector2(1.0f, -3.0043558768560112f),
        Vector2(1.0f, -3.004367087072075f),
        Vector2(1.0f, -3.0043707698511235f),
        Vector2(1.0f, -3.0043671680137054f),
        Vector2(1.0f, -3.0043565243803685f),
        Vector2(1.0f, -3.004339081771662f),
        Vector2(1.0f, -3.0043150830081347f),
        Vector2(1.0f, -3.004284770910334f),
        Vector2(1.0f, -3.0042483882988096f),
        Vector2(1.0f, -3.0042061779941083f),
        Vector2(1.0f, -3.0041583828167804f),
        Vector2(1.0f, -3.0041052455873736f),
        Vector2(1.0f, -3.0040470091264364f),
        Vector2(1.0f, -3.0039839162545166f),
        Vector2(1.0f, -3.0039162097921643f),
        Vector2(1.0f, -3.0038441325599265f),
        Vector2(1.0f, -3.003767927378353f),
        Vector2(1.0f, -3.003687837067991f),
        Vector2(1.0f, -3.0036041044493897f),
        Vector2(1.0f, -3.003516972343098f),
        Vector2(1.0f, -3.0034266835696632f),
        Vector2(1.0f, -3.0033334809496353f),
        Vector2(1.0f, -3.0032376073035607f),
        Vector2(1.0f, -3.00313930545199f),
        Vector2(1.0f, -3.003038818215471f),
        Vector2(1.0f, -3.0029363884145512f),
        Vector2(1.0f, -3.0028322588697804f),
        Vector2(1.0f, -3.0027266724017063f),
        Vector2(1.0f, -3.002619871830878f),
        Vector2(1.0f, -3.002512099977843f),
        Vector2(1.0f, -3.002403599663152f),
        Vector2(1.0f, -3.0022946137073507f),
        Vector2(1.0f, -3.002185384930989f),
        Vector2(1.0f, -3.0020761561546156f),
        Vector2(1.0f, -3.0019671701987782f),
        Vector2(1.0f, -3.001858669884026f),
        Vector2(1.0f, -3.001750898030907f),
        Vector2(1.0f, -3.00164409745997f),
        Vector2(1.0f, -3.0015385109917636f),
        Vector2(1.0f, -3.001434381446836f),
        Vector2(1.0f, -3.001331951645736f),
        Vector2(1.0f, -3.0012314644090115f),
        Vector2(1.0f, -3.0011331625572106f),
        Vector2(1.0f, -3.001037288910884f),
        Vector2(1.0f, -3.000944086290578f),
        Vector2(1.0f, -3.000853797516842f),
        Vector2(1.0f, -3.0007666654102243f),
        Vector2(1.0f, -3.000682932791273f),
        Vector2(1.0f, -3.0006028424805375f),
        Vector2(1.0f, -3.0005266372985657f),
        Vector2(1.0f, -3.0004545600659065f),
        Vector2(1.0f, -3.0003868536031075f),
        Vector2(1.0f, -3.0003237607307174f),
        Vector2(1.0f, -3.0002655242692855f),
        Vector2(1.0f, -3.00021238703936f),
        Vector2(1.0f, -3.0001645918614894f),
        Vector2(1.0f, -3.0001223815562224f),
        Vector2(1.0f, -3.000085998944106f),
        Vector2(1.0f, -3.0000556868456907f),
        Vector2(1.0f, -3.000031688081523f),
        Vector2(1.0f, -3.000014245472154f),
        Vector2(1.0f, -3.00000360183813f)
    };

    return Weapon("M249", 4, 120, 100, 0.5f, true, recoilPattern);
}

Weapon WeaponData::createSemiAutoRifle() {
    // Semi Auto Rifle recoil pattern from Rust dump data
    std::vector<Vector2> recoilPattern = {
        Vector2(0.0f, -0.0f),
        Vector2(0.5f, -3.0f),
        Vector2(0.5f, -3.0f),
        Vector2(0.5f, -3.0f),
        Vector2(0.5f, -3.0f),
        Vector2(0.5f, -3.0f),
        Vector2(0.5f, -3.0f),
        Vector2(0.5f, -3.0f),
        Vector2(0.5f, -3.0f),
        Vector2(0.5f, -3.0f),
        Vector2(0.5f, -3.0f),
        Vector2(0.5f, -3.0f),
        Vector2(0.5f, -3.0f),
        Vector2(0.5f, -3.0f),
        Vector2(0.5f, -3.0f),
        Vector2(0.5f, -3.0f)
    };

    return Weapon("Semi Auto Rifle", 5, 175, 16, 0.6f, false, recoilPattern);
}

Weapon WeaponData::createSMG() {
    // SMG (Custom SMG) recoil pattern from Rust dump data
    std::vector<Vector2> recoilPattern = {
        Vector2(0.0f, -0.0f),
        Vector2(1.0f, -1.9979318268225164f),
        Vector2(1.0f, -2.0058388248404944f),
        Vector2(1.0f, -2.057874634616657f),
        Vector2(1.0f, -2.2692645119710533f),
        Vector2(1.0f, -2.555709879114959f),
        Vector2(1.0f, -2.778130943549873f),
        Vector2(1.0f, -2.900827752473374f),
        Vector2(1.0f, -2.95472235701612f),
        Vector2(1.0f, -2.970818392757219f),
        Vector2(1.0f, -2.9801194952757797f),
        Vector2(1.0f, -3.013629300150906f),
        Vector2(1.0f, -3.102351442961708f),
        Vector2(1.0f, -3.2609032873103345f),
        Vector2(1.0f, -3.4532731813020483f),
        Vector2(1.0f, -3.651007145533084f),
        Vector2(1.0f, -3.8264658340372257f),
        Vector2(1.0f, -3.9520099008482745f),
        Vector2(1.0f, -4.000000000000005f),
        Vector2(1.0f, -4.0f),
        Vector2(1.0f, -4.0f),
        Vector2(1.0f, -4.0f),
        Vector2(1.0f, -4.0f),
        Vector2(1.0f, -4.0f)
    };

    return Weapon("Custom SMG", 6, 100, 24, 0.5f, true, recoilPattern);
}

Weapon WeaponData::createThompson() {
    // Thompson recoil pattern from Rust dump data
    std::vector<Vector2> recoilPattern = {
        Vector2(0.0f, -0.0f),
        Vector2(1.0f, -1.9980378598880544f),
        Vector2(1.0f, -2.0165361418793584f),
        Vector2(1.0f, -2.1682094967313095f),
        Vector2(1.0f, -2.498838428380084f),
        Vector2(1.0f, -2.778130943549873f),
        Vector2(1.0f, -2.9161187333938514f),
        Vector2(1.0f, -2.9639603959763274f),
        Vector2(1.0f, -2.9752302135779995f),
        Vector2(1.0f, -3.0035024684795593f),
        Vector2(1.0f, -3.102351442961708f),
        Vector2(1.0f, -3.2976214518831544f),
        Vector2(1.0f, -3.533270881939851f),
        Vector2(1.0f, -3.760724309864635f),
        Vector2(1.0f, -3.9322209458278916f),
        Vector2(1.0f, -4.000000000000005f),
        Vector2(1.0f, -4.0f),
        Vector2(1.0f, -4.0f),
        Vector2(1.0f, -4.0f),
        Vector2(1.0f, -4.0f)
    };

    return Weapon("Thompson", 7, 130, 20, 0.5f, true, recoilPattern);
}

Weapon WeaponData::createHMLMG() {
    // HMLMG recoil pattern from Rust dump data
    std::vector<Vector2> recoilPattern = {
        Vector2(-0.0f, -0.0f),
        Vector2(-1.5f, -0.5979993260019377f),
        Vector2(-1.5f, -1.4239992086968323f),
        Vector2(-1.5f, -2.3265007999486285f),
        Vector2(-1.5f, -3.15620251724627f),
        Vector2(-1.5f, -3.763802778078701f),
        Vector2(-1.5f, -3.999999999934867f),
        Vector2(-1.5f, -4.000701731337168f),
        Vector2(-1.5f, -4.001351001509809f),
        Vector2(-1.5f, -4.0019493093450675f),
        Vector2(-1.5f, -4.0024981537352184f),
        Vector2(-1.5f, -4.002999033572537f),
        Vector2(-1.5f, -4.003453447749297f),
        Vector2(-1.5f, -4.003862895157773f),
        Vector2(-1.5f, -4.00422887469024f),
        Vector2(-1.5f, -4.004552885238972f),
        Vector2(-1.5f, -4.004836425696245f),
        Vector2(-1.5f, -4.005080994954333f),
        Vector2(-1.5f, -4.00528809190551f),
        Vector2(-1.5f, -4.005459215442052f),
        Vector2(-1.5f, -4.0055958644562315f),
        Vector2(-1.5f, -4.005699537840325f),
        Vector2(-1.5f, -4.005771734486606f),
        Vector2(-1.5f, -4.00581395328735f),
        Vector2(-1.5f, -4.005827693134831f),
        Vector2(-1.5f, -4.005814452921324f),
        Vector2(-1.5f, -4.005775731539105f),
        Vector2(-1.5f, -4.005713027880446f),
        Vector2(-1.5f, -4.005627840837622f),
        Vector2(-1.5f, -4.005521669302909f),
        Vector2(-1.5f, -4.005396012168582f),
        Vector2(-1.5f, -4.0052523683269134f),
        Vector2(-1.5f, -4.00509223667018f),
        Vector2(-1.5f, -4.004917116090655f),
        Vector2(-1.5f, -4.004728505480614f),
        Vector2(-1.5f, -4.004527903732331f),
        Vector2(-1.5f, -4.004316809738081f),
        Vector2(-1.5f, -4.004096722390139f),
        Vector2(-1.5f, -4.003869140580779f),
        Vector2(-1.5f, -4.003635563202275f),
        Vector2(-1.5f, -4.003397489146903f),
        Vector2(-1.5f, -4.003156417306937f),
        Vector2(-1.5f, -4.0029138465746525f),
        Vector2(-1.5f, -4.002671275842323f),
        Vector2(-1.5f, -4.002430204002223f),
        Vector2(-1.5f, -4.002192129946627f),
        Vector2(-1.5f, -4.001958552567811f),
        Vector2(-1.5f, -4.001730970758048f),
        Vector2(-1.5f, -4.0015108834096145f),
        Vector2(-1.5f, -4.001299789414785f),
        Vector2(-1.5f, -4.001099187665831f),
        Vector2(-1.5f, -4.000910577055031f),
        Vector2(-1.5f, -4.000735456474658f),
        Vector2(-1.5f, -4.000575324816986f),
        Vector2(-1.5f, -4.00043168097429f),
        Vector2(-1.5f, -4.000306023838846f),
        Vector2(-1.5f, -4.000199852302927f),
        Vector2(-1.5f, -4.000114665258808f),
        Vector2(-1.5f, -4.000051961598764f),
        Vector2(-1.5f, -4.00001324021507f)
    };

    return Weapon("HMLMG", 8, 125, 60, 0.5f, true, recoilPattern);
}

Weapon WeaponData::createPython() {
    // Python revolver recoil pattern from Rust dump data
    std::vector<Vector2> recoilPattern = {
        Vector2(0.0f, -0.0f),
        Vector2(2.0f, -16.0f),
        Vector2(2.0f, -16.0f),
        Vector2(2.0f, -16.0f),
        Vector2(2.0f, -16.0f),
        Vector2(2.0f, -16.0f)
    };

    return Weapon("Python", 9, 150, 6, 0.5f, false, recoilPattern);
}

Weapon WeaponData::createPistolRevolver() {
    // Pistol Revolver recoil pattern from Rust dump data
    std::vector<Vector2> recoilPattern = {
        Vector2(0.0f, -0.0f),
        Vector2(1.0f, -6.0f),
        Vector2(1.0f, -6.0f),
        Vector2(1.0f, -6.0f),
        Vector2(1.0f, -6.0f),
        Vector2(1.0f, -6.0f),
        Vector2(1.0f, -6.0f),
        Vector2(1.0f, -6.0f)
    };

    return Weapon("Revolver", 10, 175, 8, 0.6f, false, recoilPattern);
}

Weapon WeaponData::createPistolSemiAuto() {
    // Semi Auto Pistol recoil pattern from Rust dump data
    std::vector<Vector2> recoilPattern = {
        Vector2(0.0f, -0.0f),
        Vector2(1.0f, -2.5f),
        Vector2(1.0f, -2.5f),
        Vector2(1.0f, -2.5f),
        Vector2(1.0f, -2.5f),
        Vector2(1.0f, -2.5f),
        Vector2(1.0f, -2.5f),
        Vector2(1.0f, -2.5f),
        Vector2(1.0f, -2.5f),
        Vector2(1.0f, -2.5f)
    };

    return Weapon("Semi Auto Pistol", 11, 150, 10, 0.6f, false, recoilPattern);
}

Weapon WeaponData::createM92() {
    // M92 pistol recoil pattern from Rust dump data
    std::vector<Vector2> recoilPattern = {
        Vector2(0.0f, -0.0f),
        Vector2(1.0f, -8.0f),
        Vector2(1.0f, -8.0f),
        Vector2(1.0f, -8.0f),
        Vector2(1.0f, -8.0f),
        Vector2(1.0f, -8.0f),
        Vector2(1.0f, -8.0f),
        Vector2(1.0f, -8.0f),
        Vector2(1.0f, -8.0f),
        Vector2(1.0f, -8.0f),
        Vector2(1.0f, -8.0f),
        Vector2(1.0f, -8.0f),
        Vector2(1.0f, -8.0f),
        Vector2(1.0f, -8.0f),
        Vector2(1.0f, -8.0f)
    };

    return Weapon("M92", 12, 150, 15, 0.6f, false, recoilPattern);
}

} // namespace models
} // namespace octane
