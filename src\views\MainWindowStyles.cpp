#include "views/MainWindowStyles.h"
#include "views/MainWindowBaseStyles.h"
#include "views/MainPageStyles.h"

namespace octane {
namespace views {

std::string MainWindowStyles::getCssContent()
{
    std::string css = MainWindowBaseStyles::getBaseStyles() +
                     MainPageStyles::getMainPageStyles() + R"(




        .content-area {
            flex: 1;
            background: linear-gradient(135deg, rgba(26, 26, 26, 0.8) 0%, rgba(42, 42, 42, 0.8) 100%);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 0 0 10px 10px;
            padding: 20px;
            overflow-y: auto;
            max-height: calc(100vh - 200px);
            overflow-y: auto;
        }



        /* Custom scrollbar */
        .tab-content::-webkit-scrollbar {
            width: 8px;
        }

        .tab-content::-webkit-scrollbar-track {
            background: rgba(26, 26, 26, 0.3);
            border-radius: 4px;
        }

        .tab-content::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #00d4ff 0%, #00ff88 100%);
            border-radius: 4px;
        }

        .section-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
            padding: 10px;
        }

        .section-card {
            background: linear-gradient(135deg, rgba(26, 26, 26, 0.6) 0%, rgba(42, 42, 42, 0.6) 100%);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            min-height: fit-content;
            box-sizing: border-box;
        }

        .section-card h3 {
            color: #00d4ff;
            margin: 0 0 15px 0;
            font-size: 16px;
            font-weight: 600;
        }

        .form-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .form-row label {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            min-width: 120px;
        }

        select, input[type="range"], input[type="number"], input[type="text"], button {
            background: linear-gradient(135deg, rgba(26, 26, 26, 0.8) 0%, rgba(42, 42, 42, 0.8) 100%);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            color: #ffffff;
            padding: 8px 12px;
            font-size: 13px;
            transition: all 0.3s ease;
            outline: none;
        }

        select:focus, input:focus {
            border-color: #00d4ff;
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }

        select {
            cursor: pointer;
            min-width: 150px;
        }

        /* Fix dropdown option colors */
        select option {
            background: #1a1a1a !important;
            color: #ffffff !important;
            padding: 8px 12px;
        }

        select option:hover {
            background: #2a2a2a !important;
            color: #00d4ff !important;
        }

        select option:checked {
            background: #00d4ff !important;
            color: #000000 !important;
        }

        button {
            cursor: pointer;
            font-weight: 500;
            min-width: 80px;
        }

        button:hover {
            background: linear-gradient(135deg, #00d4ff 0%, #00ff88 100%);
            color: #000000;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
        }

        button.primary {
            background: linear-gradient(135deg, #00d4ff 0%, #00ff88 100%);
            color: #000000;
            font-weight: 600;
        }

        button.secondary {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        button.secondary:hover {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
            border-color: rgba(255, 255, 255, 0.4);
        }

        button.refresh {
            background: linear-gradient(135deg, #ff8800 0%, #ff6600 100%);
            color: #ffffff;
            border: 1px solid rgba(255, 136, 0, 0.3);
        }

        button.refresh:hover {
            background: linear-gradient(135deg, #ffaa00 0%, #ff8800 100%);
            border-color: rgba(255, 170, 0, 0.6);
        }

        button.danger {
            background: linear-gradient(135deg, #ff4444 0%, #cc3333 100%);
            color: #ffffff;
        }

        button.danger:hover {
            background: linear-gradient(135deg, #ff6666 0%, #ff4444 100%);
        }

        .input-group {
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
        }

        .input-group input[type="range"] {
            flex: 2;
        }

        .input-group input[type="number"] {
            flex: 1;
            min-width: 80px;
            max-width: 100px;
            padding: 6px 8px;
            text-align: center;
        }

        .unit {
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
            min-width: 20px;
        }

        .checkbox-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #00d4ff;
        }

        /* Responsive Design - Prevent overlapping on smaller screens */
        @media (max-width: 800px) {
            .section-grid {
                grid-template-columns: 1fr;
                gap: 15px;
                padding: 5px;
            }

            .section-card {
                padding: 15px;
                margin-bottom: 10px;
            }

            .form-row {
                margin-bottom: 12px;
            }
        }

        @media (max-width: 600px) {
            .content-area {
                padding: 10px;
            }

            .section-card {
                padding: 12px;
            }

            .form-row {
                margin-bottom: 10px;
            }
        }
    )";
    
    css += getModalStyles();
    css += getPortStyles();
    css += getKeybindStyles();
    css += getLoadoutStyles();
    
    return css;
}

std::string MainWindowStyles::getModalStyles()
{
    return R"(
        /* Modal Styles */
        .modal {
            display: flex;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: linear-gradient(135deg, rgba(26, 26, 26, 0.95) 0%, rgba(42, 42, 42, 0.95) 100%);
            border: 2px solid rgba(0, 212, 255, 0.5);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            min-width: 400px;
            box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
        }

        .modal-content h2 {
            color: #00d4ff;
            margin-bottom: 15px;
            font-size: 20px;
        }

        .modal-content p {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 20px;
            font-size: 14px;
        }

        .modal-content input {
            width: 100%;
            max-width: 300px;
            padding: 12px 15px;
            font-size: 14px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            color: #ffffff;
            text-align: center;
            margin-bottom: 20px;
            outline: none;
        }

        .modal-content input:focus {
            border-color: #00d4ff;
            background: rgba(255, 255, 255, 0.1);
        }

        .modal-content input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .modal-buttons button {
            padding: 10px 25px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 8px;
            cursor: pointer;
            border: none;
        }

        .modal-buttons button.primary {
            background: linear-gradient(135deg, #00d4ff 0%, #00ff88 100%);
            color: #000000;
        }

        .modal-buttons button.primary:hover {
            opacity: 0.9;
        }

        .modal-buttons button:not(.primary) {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .modal-buttons button:not(.primary):hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .error-message {
            color: #ff4444;
            font-size: 12px;
            margin-top: 10px;
            min-height: 20px;
        }
    )";
}

std::string MainWindowStyles::getPortStyles()
{
    return R"(
        /* Port Selection Styles */
        .port-selection {
            display: flex;
            flex-direction: column;
            gap: 10px;
            align-items: stretch;
        }

        .port-dropdown-row {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .port-buttons-row {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .status-display {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff4444;
        }

        .status-indicator.connected {
            background: #00ff88;
        }
    )";
}

std::string MainWindowStyles::getKeybindStyles()
{
    return R"(
        /* Keybind Styles */
        .keybind-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            background: rgba(26, 26, 26, 0.4);
            border-radius: 10px;
            border: 1px solid rgba(0, 212, 255, 0.1);
            min-height: 50px;
        }

        .keybind-row label {
            flex: 1;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            margin-right: 15px;
        }

        .keybind-input {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-shrink: 0;
        }

        .keybind-input input {
            min-width: 100px;
            max-width: 120px;
            text-align: center;
            font-weight: 600;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 6px;
            background: rgba(42, 42, 42, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
        }

        .keybind-input input:hover {
            border-color: rgba(0, 212, 255, 0.6);
            background: rgba(42, 42, 42, 0.9);
        }

        .clear-btn {
            padding: 6px 12px;
            font-size: 12px;
            min-width: 60px;
            background: rgba(255, 68, 68, 0.8);
            border: 1px solid rgba(255, 68, 68, 0.3);
            color: #ffffff;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .clear-btn:hover {
            background: rgba(255, 68, 68, 1.0);
            border-color: rgba(255, 68, 68, 0.6);
            transform: translateY(-1px);
        }

        /* Keybind section spacing */
        #keybinds-tab .section-card {
            margin-bottom: 25px;
        }

        #keybinds-tab .section-grid {
            grid-template-columns: 1fr;
            gap: 25px;
        }
    )";
}

std::string MainWindowStyles::getLoadoutStyles()
{
    return R"(
        /* Loadout Styles */
        .loadout-list {
            margin-bottom: 20px;
        }

        .loadout-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: rgba(26, 26, 26, 0.3);
            border-radius: 8px;
            margin-bottom: 10px;
            border: 1px solid rgba(0, 212, 255, 0.1);
        }

        .loadout-info {
            flex: 1;
        }

        .loadout-name {
            font-weight: 600;
            color: #00d4ff;
            margin-bottom: 5px;
        }

        .loadout-details {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        .loadout-actions {
            display: flex;
            gap: 8px;
        }

        .loadout-actions button {
            padding: 6px 12px;
            font-size: 12px;
        }

        .loadout-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        /* Calibration Styles */
        .calibration-instructions {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .calibration-instructions p {
            margin: 5px 0;
            color: #ffc107;
        }

        .calibration-instructions strong {
            color: #fff;
        }

        .calibration-status {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .status-text {
            color: #00d4ff;
            font-weight: 500;
            margin-bottom: 10px;
            text-align: center;
        }

        .progress-bar {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, #00d4ff, #00ff88);
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
        }

        .recoil-visualization {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
        }

        .recoil-visualization h4 {
            color: #fff;
            margin: 0 0 15px 0;
            text-align: center;
        }

        #recoil-pattern-canvas {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            display: block;
            margin: 0 auto 15px auto;
        }

        .pattern-stats {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
        }

        .stat-label {
            color: #ccc;
            font-size: 14px;
        }

        .stat-value {
            color: #00ff88;
            font-weight: 600;
            font-size: 14px;
        }

        .copy-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: opacity 0.2s;
        }

        .copy-btn:hover {
            opacity: 0.8;
        }

        /* New UI Styles */
        .toggle-keybind-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .keybind-btn {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            color: #00d4ff;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            min-width: 40px;
            transition: all 0.2s;
        }

        .keybind-btn:hover {
            background: rgba(0, 212, 255, 0.2);
            border-color: rgba(0, 212, 255, 0.5);
        }

        .compact-settings {
            max-width: 300px;
        }

        .compact-form-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .compact-form-row label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            margin-right: 10px;
            flex: 1;
        }

        .compact-form-row input[type="number"] {
            width: 80px;
            padding: 6px 8px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            color: #ffffff;
            font-size: 12px;
        }

        /* Simple License Modal Styles */
        .license-modal-content {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            min-width: 400px;
            max-width: 90vw;
        }

        .license-header h2 {
            color: #ffffff;
            margin-bottom: 10px;
            font-size: 24px;
            font-weight: normal;
        }

        .license-header p {
            color: #cccccc;
            margin-bottom: 25px;
            font-size: 14px;
        }

        .license-form {
            margin: 25px 0;
        }

        #license-key-input {
            width: 100%;
            padding: 12px 15px;
            font-size: 14px;
            background: #333;
            border: 1px solid #555;
            border-radius: 4px;
            color: #ffffff;
            text-align: center;
            margin-bottom: 15px;
            box-sizing: border-box;
        }

        #license-key-input:focus {
            outline: none;
            border-color: #00d4ff;
            background: #3a3a3a;
        }

        #license-key-input::placeholder {
            color: #888;
        }

        .error-message {
            color: #ff6b6b;
            font-size: 13px;
            margin: 10px 0;
            padding: 8px;
            background: #3a2a2a;
            border: 1px solid #5a3a3a;
            border-radius: 4px;
            display: none;
        }

        .license-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .btn-primary, .btn-secondary {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            min-width: 120px;
        }

        .btn-primary {
            background: #00d4ff;
            color: #000;
        }

        .btn-primary:hover {
            background: #00b8e6;
        }

        .btn-secondary {
            background: #555;
            color: #fff;
        }

        .btn-secondary:hover {
            background: #666;
        }

        .license-info {
            margin-top: 20px;
            padding: 10px;
            background: #333;
            border-radius: 4px;
        }

        .license-info p {
            color: #aaa;
            font-size: 12px;
            margin: 0;
        }

        /* Clean Main Page Grid Layout */
        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 20px;
            height: calc(100vh - 120px);
            padding: 20px;
        }

        .main-card {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .main-card h3 {
            color: #ffffff;
            margin: 0 0 15px 0;
            font-size: 16px;
            font-weight: 600;
            border-bottom: 1px solid #444;
            padding-bottom: 8px;
        }

        /* Recoil Control Card */
        .control-row {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .toggle-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s;
        }

        .toggle-btn.disabled {
            background: #555;
            color: #ccc;
        }

        .toggle-btn.enabled {
            background: #00d4ff;
            color: #000;
        }

        .keybind-btn {
            padding: 12px 16px;
            background: #444;
            border: 1px solid #666;
            border-radius: 6px;
            color: #fff;
            cursor: pointer;
            min-width: 50px;
        }

        .keybind-btn:hover {
            background: #555;
        }

        .esp32-section {
            flex: 1;
        }

        .port-row {
            display: flex;
            gap: 8px;
            margin-bottom: 10px;
        }

        .port-select {
            flex: 1;
            padding: 8px;
            background: #333;
            border: 1px solid #555;
            border-radius: 4px;
            color: #fff;
        }

        .btn-small {
            padding: 8px 12px;
            background: #444;
            border: 1px solid #666;
            border-radius: 4px;
            color: #fff;
            cursor: pointer;
            font-size: 12px;
        }

        .btn-small:hover {
            background: #555;
        }

        .connection-row {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .connect-btn {
            padding: 10px 20px;
            background: #00d4ff;
            border: none;
            border-radius: 6px;
            color: #000;
            font-weight: bold;
            cursor: pointer;
        }

        .connect-btn:hover {
            background: #00b8e6;
        }

        .status-display {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-dot.disconnected {
            background: #ff6b6b;
        }

        .status-dot.connected {
            background: #51cf66;
        }

        /* Weapon Setup Card */
        .weapon-row {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .weapon-row label {
            color: #ccc;
            min-width: 80px;
            font-size: 14px;
        }

        .weapon-select {
            flex: 1;
            padding: 8px;
            background: #333;
            border: 1px solid #555;
            border-radius: 4px;
            color: #fff;
            margin-left: 10px;
        }

        /* Settings Card */
        .settings-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            flex: 1;
        }

        .setting-item {
            display: flex;
            flex-direction: column;
        }

        .setting-item label {
            color: #ccc;
            font-size: 12px;
            margin-bottom: 5px;
        }

        .setting-input {
            padding: 8px;
            background: #333;
            border: 1px solid #555;
            border-radius: 4px;
            color: #fff;
            font-size: 14px;
        }

        .checkbox-row {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 5px;
        }

        .checkbox-row input[type="checkbox"] {
            margin: 0;
        }

        .checkbox-row label {
            color: #ccc;
            font-size: 13px;
            margin: 0;
        }

        /* Randomization Card */
        .randomization-section {
            flex: 1;
        }

        .random-item {
            margin-bottom: 20px;
        }

        .random-item label {
            color: #ccc;
            font-size: 14px;
            display: block;
            margin-bottom: 8px;
        }

        .slider-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .slider {
            flex: 1;
            height: 6px;
            background: #444;
            border-radius: 3px;
            outline: none;
            -webkit-appearance: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 16px;
            height: 16px;
            background: #00d4ff;
            border-radius: 50%;
            cursor: pointer;
        }

        .slider::-moz-range-thumb {
            width: 16px;
            height: 16px;
            background: #00d4ff;
            border-radius: 50%;
            cursor: pointer;
            border: none;
        }

        .slider-input {
            width: 50px;
            padding: 6px;
            background: #333;
            border: 1px solid #555;
            border-radius: 4px;
            color: #fff;
            text-align: center;
            font-size: 12px;
        }
    )";
}

} // namespace views
} // namespace octane
