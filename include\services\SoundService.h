#pragma once
#include <memory>
#include <string>
#include <Windows.h>

namespace octane {
namespace services {

/**
 * @brief Simple sound service for playing keybind activation sounds
 */
class SoundService {
public:
    SoundService();
    ~SoundService();

    /**
     * @brief Initialize the sound service
     */
    bool initialize();

    /**
     * @brief Cleanup the sound service
     */
    void cleanup();

    /**
     * @brief Play a keybind activation sound
     */
    void playKeybindSound();

    /**
     * @brief Set whether sounds are enabled
     */
    void setEnabled(bool enabled);

    /**
     * @brief Check if sounds are enabled
     */
    bool isEnabled() const;

private:
    bool m_initialized;
    bool m_enabled;
};

} // namespace services
} // namespace octane
