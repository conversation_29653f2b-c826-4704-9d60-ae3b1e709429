#pragma once
#include <string>
#include <vector>
#include <fstream>
#include "models/RecoilSettings.h"

namespace octane {
namespace utils {

/**
 * @brief Manages configuration save/load functionality
 */
class ConfigManager {
public:
    ConfigManager();
    ~ConfigManager() = default;

    /**
     * @brief Save current settings to a config file
     */
    bool saveConfig(const models::RecoilSettings& settings, const std::string& configName);

    /**
     * @brief Load settings from a config file
     */
    bool loadConfig(models::RecoilSettings& settings, const std::string& configName);

    /**
     * @brief Get list of available config files
     */
    std::vector<std::string> getAvailableConfigs();

    /**
     * @brief Delete a config file
     */
    bool deleteConfig(const std::string& configName);

    /**
     * @brief Save COM port for auto-detection
     */
    bool saveComPort(const std::string& comPort);

    /**
     * @brief Load saved COM port
     */
    std::string loadComPort();

    /**
     * @brief Save auto-load config setting
     */
    bool saveAutoLoadConfig(const std::string& configName, bool enabled);

    /**
     * @brief Load auto-load config setting
     */
    std::pair<std::string, bool> loadAutoLoadConfig();

private:
    std::string m_configDirectory;
    std::string m_settingsFile;

    /**
     * @brief Ensure config directory exists
     */
    void ensureConfigDirectory();

    /**
     * @brief Convert settings to string format
     */
    std::string settingsToString(const models::RecoilSettings& settings);

    /**
     * @brief Parse settings from string format
     */
    bool stringToSettings(const std::string& data, models::RecoilSettings& settings);

    /**
     * @brief Get config file path
     */
    std::string getConfigFilePath(const std::string& configName);
};

} // namespace utils
} // namespace octane
