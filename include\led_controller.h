#ifndef LED_CONTROLLER_H
#define LED_CONTROLLER_H

#include <Arduino.h>
#include "config.h"

// ===== LED CONTROLLER CLASS =====
class LEDController {
private:
    LEDState currentState;
    uint32_t lastUpdate;
    uint32_t patternStartTime;
    bool ledOn;
    uint8_t blinkCount;
    uint8_t maxBlinks;
    uint32_t blinkInterval;
    uint32_t pauseInterval;
    bool inPause;

public:
    LEDController();
    void begin();
    void setState(LEDState state);
    void update();
    LEDState getCurrentState() const;

private:
    void setLED(bool state);
    void updateBootPattern();
    void updateWaitingPattern();
    void updateConnectedPattern();
    void updateActivePattern();
    void updateErrorPattern();
    void updateStealthPattern();
    void updateUpdatingPattern();
    void updateBindingPattern();
    void updateAuthenticatedPattern();
    void updateCommandReceivedPattern();
};

// Global LED controller instance
extern LEDController ledController;

#endif // LED_CONTROLLER_H
