#pragma once
#include <string>
#include <vector>
#include "Vector2.h"

namespace octane {
namespace models {

/**
 * @brief Represents a weapon with its recoil pattern and properties
 */
class Weapon {
public:
    /**
     * @brief Default constructor
     */
    Weapon();

    /**
     * @brief Constructor with all parameters
     */
    Weapon(const std::string& name, int id, unsigned int delay, unsigned int maxBulletCount,
           float hipfireScale, bool isAutomatic, const std::vector<Vector2>& recoilPattern);

    // Getters
    const std::string& getName() const { return m_name; }
    int getId() const { return m_id; }
    unsigned int getDelay() const { return m_delay; }
    unsigned int getMaxBulletCount() const { return m_maxBulletCount; }
    float getHipfireScale() const { return m_hipfireScale; }
    bool isAutomatic() const { return m_isAutomatic; }
    const std::vector<Vector2>& getRecoilPattern() const { return m_recoilPattern; }

    // Setters
    void setName(const std::string& name) { m_name = name; }
    void setId(int id) { m_id = id; }
    void setDelay(unsigned int delay) { m_delay = delay; }
    void setMaxBulletCount(unsigned int maxBulletCount) { m_maxBulletCount = maxBulletCount; }
    void setHipfireScale(float hipfireScale) { m_hipfireScale = hipfireScale; }
    void setIsAutomatic(bool isAutomatic) { m_isAutomatic = isAutomatic; }
    void setRecoilPattern(const std::vector<Vector2>& recoilPattern) { m_recoilPattern = recoilPattern; }

private:
    std::string m_name;
    int m_id;
    unsigned int m_delay;                // Delay between shots in milliseconds
    unsigned int m_maxBulletCount;       // Maximum bullets in magazine
    float m_hipfireScale;                // Hipfire scale modifier
    bool m_isAutomatic;                  // Whether weapon is automatic
    std::vector<Vector2> m_recoilPattern; // Recoil pattern data
};

} // namespace models
} // namespace octane
