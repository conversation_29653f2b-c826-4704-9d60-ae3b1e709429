#include "config/AppConfig.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <windows.h>
#include <shlobj.h>

namespace octane {
namespace config {

AppConfig& AppConfig::getInstance()
{
    static AppConfig instance;
    return instance;
}

void AppConfig::loadFromFile(const std::string& configPath)
{
    std::string path = configPath.empty() ? getDefaultConfigPath() : configPath;
    
    std::ifstream file(path);
    if (!file.is_open()) {
        std::cout << "AppConfig: No config file found, using defaults (Development mode)" << std::endl;
        return;
    }
    
    std::string line;
    while (std::getline(file, line)) {
        if (line.empty() || line[0] == '#') {
            continue; // Skip empty lines and comments
        }
        
        size_t equalPos = line.find('=');
        if (equalPos != std::string::npos) {
            std::string key = line.substr(0, equalPos);
            std::string value = line.substr(equalPos + 1);
            
            // Trim whitespace
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of(" \t") + 1);
            
            if (key == "mode") {
                if (value == "production" || value == "PRODUCTION") {
                    m_mode = AppMode::PRODUCTION;
                    std::cout << "AppConfig: Set to PRODUCTION mode" << std::endl;
                } else {
                    m_mode = AppMode::DEVELOPMENT;
                    std::cout << "AppConfig: Set to DEVELOPMENT mode" << std::endl;
                }
            }
        }
    }
    
    file.close();
}

void AppConfig::saveToFile(const std::string& configPath) const
{
    std::string path = configPath.empty() ? getDefaultConfigPath() : configPath;
    
    std::ofstream file(path);
    if (!file.is_open()) {
        std::cerr << "AppConfig: Failed to save config file: " << path << std::endl;
        return;
    }
    
    file << "# Octane Recoil Controller Configuration\n";
    file << "# Mode: development or production\n";
    file << "mode=" << (m_mode == AppMode::PRODUCTION ? "production" : "development") << "\n";
    file << "\n";
    file << "# Development mode:\n";
    file << "#   - Disables security monitoring\n";
    file << "#   - Skips license validation\n";
    file << "#   - Enables debug logging\n";
    file << "#\n";
    file << "# Production mode:\n";
    file << "#   - Enables full security monitoring\n";
    file << "#   - Requires license validation\n";
    file << "#   - Reports security events to Discord\n";
    
    file.close();
    std::cout << "AppConfig: Configuration saved to " << path << std::endl;
}

std::string AppConfig::getDefaultConfigPath() const
{
    // Get the application data folder
    char appDataPath[MAX_PATH];
    if (SUCCEEDED(SHGetFolderPathA(NULL, CSIDL_LOCAL_APPDATA, NULL, 0, appDataPath))) {
        std::string configDir = std::string(appDataPath) + "\\OctaneRecoilController";
        
        // Create directory if it doesn't exist
        CreateDirectoryA(configDir.c_str(), NULL);
        
        return configDir + "\\config.ini";
    }
    
    // Fallback to current directory
    return "config.ini";
}

} // namespace config
} // namespace octane
