#include "core/Application.h"
#include "services/ConfigurationService.h"
#include "services/DeviceService.h"
#include "services/AuthenticationService.h"
#include "security/AdvancedSecurityManager.h"
#include <iostream>
#include <chrono>
#include <thread>

namespace octane {
namespace core {

// Static constants
const std::string Application::APPLICATION_NAME = "Octane Recoil Controller";
const std::string Application::APPLICATION_VERSION = "2.1.0";

Application::Application()
    : m_initialized(false)
    , m_shuttingDown(false)
{
    // Initialize application
    std::cout << "Initializing Octane Application..." << std::endl;
}

Application::~Application()
{
    shutdown();
}

bool Application::initialize()
{
    if (m_initialized) {
        return true;
    }

    try {
        std::cout << "Initializing Octane Application..." << std::endl;

        // Initialize services
        if (!initializeServices()) {
            std::cerr << "Failed to initialize services" << std::endl;
            return false;
        }

        // Initialize security
        if (!initializeSecurity()) {
            std::cerr << "Failed to initialize security" << std::endl;
            return false;
        }

        m_initialized = true;

        std::cout << "Octane Application initialized successfully" << std::endl;
        return true;
    }
    catch (const std::exception& ex) {
        std::cerr << "Failed to initialize application: " << ex.what() << std::endl;
        return false;
    }
}

void Application::shutdown()
{
    if (!m_initialized || m_shuttingDown) {
        return;
    }

    m_shuttingDown = true;
    std::cout << "Shutting down Octane Application..." << std::endl;

    // Clean up resources
    cleanup();

    m_initialized = false;

    std::cout << "Octane Application shutdown complete" << std::endl;
}

services::ConfigurationService* Application::getConfigurationService() const
{
    return m_configurationService.get();
}

services::DeviceService* Application::getDeviceService() const
{
    return m_deviceService.get();
}

services::AuthenticationService* Application::getAuthenticationService() const
{
    return m_authenticationService.get();
}

security::AdvancedSecurityManager* Application::getSecurityManager() const
{
    return m_securityManager.get();
}

bool Application::initializeServices()
{
    try {
        // Initialize configuration service
        m_configurationService = std::make_unique<services::ConfigurationService>();

        // Initialize device service
        m_deviceService = std::make_unique<services::DeviceService>();

        // Initialize authentication service
        m_authenticationService = std::make_unique<services::AuthenticationService>();

        std::cout << "All services initialized successfully" << std::endl;
        return true;
    }
    catch (const std::exception& ex) {
        std::cerr << "Failed to initialize services: " << ex.what() << std::endl;
        return false;
    }
}

bool Application::initializeSecurity()
{
    try {
        // Initialize security manager
        m_securityManager = std::make_unique<security::AdvancedSecurityManager>();

        std::cout << "Security manager initialized successfully" << std::endl;
        return true;
    }
    catch (const std::exception& ex) {
        std::cerr << "Failed to initialize security: " << ex.what() << std::endl;
        return false;
    }
}

void Application::cleanup()
{
    // Clean up services in reverse order
    m_securityManager.reset();
    m_authenticationService.reset();
    m_deviceService.reset();
    m_configurationService.reset();
}

} // namespace core
} // namespace octane
