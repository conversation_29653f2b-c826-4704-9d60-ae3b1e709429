#pragma once
#include <string>

namespace octane {
namespace views {

/**
 * @brief Contains the main HTML content for the WebView2 interface
 */
class MainWindowHtml {
public:
    /**
     * @brief Get the complete HTML content for the main window
     */
    static std::string getHtmlContent();

private:
    static std::string getJavaScriptPart();
    static std::string getMainTabFunctions();
    static std::string getBasicFunctions();
    static std::string getTabFunctions();
    static std::string getInitFunction();

    MainWindowHtml() = delete; // Static class
};

} // namespace views
} // namespace octane
