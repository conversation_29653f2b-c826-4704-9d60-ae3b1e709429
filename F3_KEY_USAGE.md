# ESP32 Command Tester with F3 Key Support

## 🎹 F3 Key Functionality

The ESP32 Command Tester now supports **F3 key detection** for instant mouse movement!

### ✅ Features
- **Press F3** to instantly move your mouse using the current X,Y values in the GUI
- **Real-time status** showing F3 key activity
- **Background monitoring** - F3 works even when the window isn't focused
- **Safe operation** - Only works when connected to ESP32

### 🚀 Quick Start

1. **Install Requirements:**
   ```bash
   install_requirements.bat
   ```

2. **Run the Application:**
   ```bash
   python esp32_command_tester.py
   ```

3. **Connect to ESP32:**
   - Select your COM port
   - Click "Connect"

4. **Set Mouse Movement:**
   - Enter X and Y values (e.g., X=10, Y=-5)
   - Press **F3** to move mouse by those amounts

### 🎯 Usage Example

1. Set X=10, Y=-5 in the GUI
2. Press F3 → Mouse moves 10 pixels right, 5 pixels up
3. Press F3 again → Mouse moves another 10 right, 5 up
4. Change values and press F3 for different movements

### 🔧 Requirements

- **Python 3.6+**
- **keyboard** module (`pip install keyboard`)
- **pyserial** module (`pip install pyserial`)
- **ESP32 connected** and running Octane firmware

### 📝 Notes

- **Administrator privileges** may be required for global key detection
- **F3 status** is shown in the GUI (Ready/Moved/Not Connected)
- **Background monitoring** continues until application is closed
- **Error handling** prevents crashes if ESP32 disconnects

### 🎮 Perfect for Gaming

This is ideal for:
- **Recoil control** - Set recoil pattern and press F3 during shooting
- **Quick movements** - Instant mouse adjustments
- **Testing** - Rapid testing of different movement values

### 🛠️ Troubleshooting

**F3 not working?**
- Run as Administrator
- Check if `keyboard` module is installed
- Ensure ESP32 is connected

**Permission errors?**
- Right-click → "Run as Administrator"
- Some antivirus may block global key hooks
