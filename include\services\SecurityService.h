#pragma once

#include <string>
#include <vector>
#include <chrono>
#include <memory>
#include <thread>
#include <atomic>

namespace octane {
namespace services {

class SecurityService
{
public:
    SecurityService();
    ~SecurityService();

    // Initialize security monitoring
    bool initialize();
    void shutdown();

    // Security event reporting
    void reportSecurityEvent(const std::string& eventType, const std::string& details);
    void reportProcessDetection(const std::string& processName);
    void reportDebuggerDetection();
    void reportMemoryTampering();
    void reportUnauthorizedAccess(const std::string& resource);
    void reportSuspiciousActivity(const std::string& activity);

    // Security monitoring controls
    void startMonitoring();
    void stopMonitoring();
    bool isMonitoring() const { return m_monitoring; }

    // Configuration
    void setDiscordWebhook(const std::string& webhookUrl);
    void setReportingEnabled(bool enabled) { m_reportingEnabled = enabled; }

private:
    // Discord webhook integration
    void sendToDiscord(const std::string& message);
    std::string formatSecurityMessage(const std::string& eventType, const std::string& details);
    
    // Security monitoring functions
    void monitoringLoop();
    void checkForSuspiciousProcesses();
    void checkForDebuggers();
    void checkMemoryIntegrity();
    void checkSystemIntegrity();
    
    // HTTP request helper
    bool sendHttpRequest(const std::string& url, const std::string& data);
    
    // Security data collection
    std::string getSystemInfo();
    std::string getProcessList();
    std::string getNetworkInfo();
    
    // Member variables
    std::string m_discordWebhook;
    std::atomic<bool> m_monitoring;
    std::atomic<bool> m_reportingEnabled;
    std::unique_ptr<std::thread> m_monitoringThread;
    
    // Security state
    std::chrono::steady_clock::time_point m_lastCheck;
    std::vector<std::string> m_suspiciousProcesses;
    std::vector<std::string> m_detectedThreats;
    
    // Constants
    static const std::vector<std::string> BLACKLISTED_PROCESSES;
    static const std::vector<std::string> DEBUGGER_PROCESSES;
    static const int MONITORING_INTERVAL_MS = 5000; // 5 seconds
};

} // namespace services
} // namespace octane
