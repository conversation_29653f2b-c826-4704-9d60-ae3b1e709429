#pragma once
#include <vector>
#include <unordered_map>
#include <string>
#include "Weapon.h"
#include "Vector2.h"

namespace octane {
namespace models {

/**
 * @brief Contains hardcoded weapon data and recoil patterns from latest Rust dumps
 */
class WeaponData {
public:
    /**
     * @brief Get all available weapons
     */
    static std::vector<Weapon> getAllWeapons();

    /**
     * @brief Get a weapon by its ID
     */
    static Weapon getWeaponById(int id);

    /**
     * @brief Get a weapon by its name
     */
    static Weapon getWeaponByName(const std::string& name);

    /**
     * @brief Get weapon names for UI dropdown
     */
    static std::vector<std::string> getWeaponNames();

    /**
     * @brief Attachment modifiers for weapons
     */
    struct AttachmentModifiers {
        static const std::unordered_map<std::string, float> scopeModifiers;
        static const std::unordered_map<std::string, float> muzzleModifiers;
        static const std::unordered_map<std::string, float> barrelModifiers;
    };

private:
    // Weapon creation methods - Assault Rifles
    static Weapon createAK47U();
    static Weapon createLR300();
    static Weapon createM249();
    static Weapon createSemiAutoRifle();

    // SMGs
    static Weapon createMP5();
    static Weapon createSMG();
    static Weapon createThompson();
    static Weapon createHMLMG();

    // Pistols
    static Weapon createPython();
    static Weapon createPistolRevolver();
    static Weapon createPistolSemiAuto();
    static Weapon createM92();

    // Static weapon instances - Assault Rifles
    static const Weapon AK47U;
    static const Weapon LR300;
    static const Weapon M249;
    static const Weapon SEMI_AUTO_RIFLE;

    // SMGs
    static const Weapon MP5;
    static const Weapon SMG;
    static const Weapon THOMPSON;
    static const Weapon HMLMG;

    // Pistols
    static const Weapon PYTHON;
    static const Weapon PISTOL_REVOLVER;
    static const Weapon PISTOL_SEMIAUTO;
    static const Weapon M92;
};

} // namespace models
} // namespace octane
