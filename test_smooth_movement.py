#!/usr/bin/env python3
"""
Test Smooth Movement Approach
Monitor ESP32 to verify smooth recoil with multiple MOUSE_MOVE commands
"""

import serial
import time
import threading

def monitor_esp32():
    """Monitor ESP32 messages in real-time"""
    print("🔍 MONITORING ESP32 MESSAGES")
    print("=" * 50)
    
    # Try different COM ports
    ports_to_try = ['COM10', 'COM13', 'COM3', 'COM4', 'COM5']
    ser = None
    
    for port in ports_to_try:
        try:
            print(f"Trying {port}...")
            ser = serial.Serial(port, 115200, timeout=1)
            time.sleep(2)
            
            # Test connection
            ser.write(b"identify\n")
            time.sleep(0.5)
            
            if ser.in_waiting > 0:
                response = ser.readline().decode('utf-8', errors='ignore').strip()
                if response:
                    print(f"✅ Connected to {port}: {response}")
                    break
            else:
                ser.close()
                ser = None
        except:
            if ser:
                ser.close()
            ser = None
    
    if not ser:
        print("❌ Could not find ESP32 on any COM port")
        return
    
    print("\n📡 Monitoring ESP32 messages...")
    print("🎮 Now trigger recoil in the desktop application!")
    print("   (Make sure smoothness is > 0% to see smooth movement)")
    print("   Press Ctrl+C to stop monitoring\n")
    
    message_count = 0
    mouse_move_count = 0
    last_message_time = time.time()
    
    try:
        while True:
            if ser.in_waiting > 0:
                try:
                    response = ser.readline().decode('utf-8', errors='ignore').strip()
                    if response:
                        current_time = time.time()
                        time_diff = current_time - last_message_time
                        last_message_time = current_time
                        
                        message_count += 1
                        
                        # Count MOUSE_MOVE commands for smooth movement analysis
                        if "MOUSE_MOVE" in response:
                            mouse_move_count += 1
                            print(f"[{message_count:3d}] (+{time_diff:.3f}s) 🖱️  {response}")
                        elif "RECOIL" in response:
                            print(f"[{message_count:3d}] (+{time_diff:.3f}s) 🎯 {response}")
                        elif "HEARTBEAT" in response:
                            print(f"[{message_count:3d}] (+{time_diff:.3f}s) 💓 {response}")
                        elif "OK" in response:
                            print(f"[{message_count:3d}] (+{time_diff:.3f}s) ✅ {response}")
                        elif "ERROR" in response:
                            print(f"[{message_count:3d}] (+{time_diff:.3f}s) ❌ {response}")
                        else:
                            print(f"[{message_count:3d}] (+{time_diff:.3f}s) 📝 {response}")
                        
                        # Show statistics every 20 messages
                        if message_count % 20 == 0:
                            print(f"\n📊 Stats: {message_count} total messages, {mouse_move_count} MOUSE_MOVE commands")
                            if mouse_move_count > 0:
                                print(f"   🎯 Smooth movement detected! ({mouse_move_count} movement commands)")
                            print()
                
                except UnicodeDecodeError:
                    pass  # Skip invalid characters
            else:
                time.sleep(0.01)  # Small delay to prevent busy waiting
                
    except KeyboardInterrupt:
        print(f"\n\n📊 FINAL STATISTICS:")
        print(f"   Total messages: {message_count}")
        print(f"   MOUSE_MOVE commands: {mouse_move_count}")
        
        if mouse_move_count > 0:
            print(f"\n✅ SMOOTH MOVEMENT CONFIRMED!")
            print(f"   • Desktop app is sending multiple MOUSE_MOVE commands")
            print(f"   • Each bullet creates {mouse_move_count // max(1, message_count // 10)} movement steps")
            print(f"   • This creates smooth interpolated recoil movement")
        else:
            print(f"\n⚠️  No MOUSE_MOVE commands detected")
            print(f"   • Make sure recoil smoothness is > 0%")
            print(f"   • Try triggering recoil in the desktop app")
        
        ser.close()
        print("\n🔚 Monitoring stopped")

def main():
    monitor_esp32()

if __name__ == "__main__":
    main()
