#pragma once
#include <string>
#include <chrono>

namespace octane {
namespace security {

/**
 * @brief Manages license key validation and expiration
 */
class LicenseManager {
public:
    struct LicenseInfo {
        bool isValid = false;
        std::string keyType = "Unknown";
        std::chrono::system_clock::time_point expirationDate;
        int daysRemaining = 0;
        std::string userName = "";
    };

    /**
     * @brief Validate a license key
     * @param key The license key to validate
     * @return License information
     */
    static LicenseInfo validateKey(const std::string& key);

    /**
     * @brief Check if current license is still valid
     * @return True if license is valid and not expired
     */
    static bool isLicenseValid();

    /**
     * @brief Get current license information
     * @return Current license info
     */
    static LicenseInfo getCurrentLicense();

    /**
     * @brief Set the current license key
     * @param key The license key to set
     * @return True if key was accepted
     */
    static bool setLicenseKey(const std::string& key);

private:
    static LicenseInfo s_currentLicense;
    static std::string s_currentKey;
    
    LicenseManager() = delete; // Static class
};

} // namespace security
} // namespace octane
