#pragma once
#include <string>
#include <memory>
#include <thread>
#include <atomic>
#include <chrono>
#include <map>
#include "models/Weapon.h"
#include "models/RecoilSettings.h"
#include "models/KeybindSettings.h"

namespace octane {

// Forward declarations
namespace services {
    class ESP32Service;
}

namespace services {

/**
 * @brief Service for controlling weapon recoil compensation
 * Based on the working C# implementation
 */
class RecoilControlService {
public:
    RecoilControlService();
    ~RecoilControlService();

    /**
     * @brief Initialize the recoil control service
     */
    bool initialize();

    /**
     * @brief Shutdown the service
     */
    void shutdown();

    /**
     * @brief Set the current weapon
     */
    void setWeapon(const models::Weapon& weapon);

    /**
     * @brief Set the current weapon by ID
     */
    void setWeaponById(int weaponId);

    /**
     * @brief Set the current weapon by name
     */
    void setWeaponByName(const std::string& weaponName);

    /**
     * @brief Get the current weapon
     */
    const models::Weapon& getCurrentWeapon() const;

    /**
     * @brief Set recoil settings
     */
    void setRecoilSettings(const models::RecoilSettings& settings);

    /**
     * @brief Get current recoil settings
     */
    const models::RecoilSettings& getRecoilSettings() const;

    /**
     * @brief Enable/disable recoil control
     */
    void setEnabled(bool enabled);

    /**
     * @brief Check if recoil control is enabled
     */
    bool isEnabled() const;

    /**
     * @brief Start the recoil control loop (based on C# implementation)
     */
    void startRecoilControl();

    /**
     * @brief Stop the recoil control loop
     */
    void stopRecoilControl();

    /**
     * @brief Set ESP32 service for hardware mouse control
     */
    void setESP32Service(std::shared_ptr<ESP32Service> esp32Service);

    /**
     * @brief Update sensitivity settings (from UI)
     */
    void setSensitivity(float sensitivity);
    void setAdsSensitivity(float adsSensitivity);
    void setFOV(float fov);
    void setSmoothness(int smoothness);
    void setCursorCheckEnabled(bool enabled);

    /**
     * @brief Auto code lock feature settings
     */
    void setAutoCodeLockEnabled(bool enabled);
    void setCodeLockDigits(const std::string& digits);

    /**
     * @brief Additional gaming features
     */
    void setRapidFireEnabled(bool enabled);
    void setAutoClickerEnabled(bool enabled);
    void setAntiAfkEnabled(bool enabled);
    void setAfkInterval(int seconds);

    /**
     * @brief Keybind management
     */
    void setKeybind(const std::string& action, const std::string& keyName);
    void clearKeybind(const std::string& action);

    /**
     * @brief Hipfire and randomization settings
     */
    void setCompensateHipfire(bool enabled);
    void setHipfireMode(const std::string& mode);
    void setHorizontalRandomization(int value);
    void setVerticalRandomization(int value);

    /**
     * @brief Humanization settings
     */
    void setHumanizationEnabled(bool enabled);
    void setHumanizationRandomization(int value);
    void setHumanizationLevel(int value);

private:
    // Core state
    bool m_initialized;
    bool m_enabled;
    models::Weapon m_currentWeapon;
    models::RecoilSettings m_recoilSettings;
    models::KeybindSettings m_keybindSettings;

    // Recoil control thread
    std::atomic<bool> m_running;
    std::unique_ptr<std::thread> m_recoilThread;

    // ESP32 service for hardware mouse control
    std::shared_ptr<ESP32Service> m_esp32Service;

    // Recoil execution state
    std::atomic<bool> m_isFiring;
    std::atomic<int> m_currentBullet;
    std::chrono::steady_clock::time_point m_lastShotTime;

    // Removed smart calibration system - using simple template approach

    // Private methods (based on C# implementation)
    void recoilControlLoop();
    bool isMouseButtonPressed(int button) const;
    bool areBothMouseButtonsPressed() const;
    void executeRecoilPattern();
    void sendMouseMovement(float deltaX, float deltaY);
    void sendSmoothMouseMovement(int deltaX, int deltaY, int steps, int totalTimeMs);
    void sendHardwareInterpolatedMovement(int deltaX, int deltaY, int steps, int totalTimeMs);
    void sendOptimizedRecoilSequence(float smoothing, int bulletDelay);
    void sendSmartBufferedBullet(float deltaX, float deltaY, float smoothing, int bulletDelay);
    void sendWeaponBasedBullet();
    void startWeaponBasedRecoil();
    void stopWeaponBasedRecoil();
    void accurateSleep(int milliseconds);

    // Template movement detection system
    bool isWalking() const;
    bool isCrouching() const;
    bool isCrouchWalking() const;
    float getMovementMultiplier() const;

    // Cursor visibility check (fixed logic - run when cursor is HIDDEN)
    bool isCursorVisible() const;

    // Cursor check setting
    bool m_cursorCheckEnabled;

    // Auto code lock feature
    bool m_autoCodeLockEnabled;
    std::string m_codeLockDigits;

    // Additional gaming features
    bool m_rapidFireEnabled;
    bool m_autoClickerEnabled;
    bool m_antiAfkEnabled;
    int m_afkInterval;

    // Hipfire and randomization settings
    bool m_compensateHipfire;
    std::string m_hipfireMode;
    int m_horizontalRandomization;
    int m_verticalRandomization;

    // Humanization settings
    bool m_humanizationEnabled;
    int m_humanizationRandomization;
    int m_humanizationLevel;

    // Keybind storage and monitoring
    std::map<std::string, int> m_keybinds; // action -> virtual key code
    std::map<std::string, bool> m_keyStates; // track key press states
    std::chrono::steady_clock::time_point m_lastAfkTime;
    std::chrono::steady_clock::time_point m_lastCodeLockTime;
    bool m_interfaceVisible;

    // Recoil timing control to prevent ESP32 overload
    std::chrono::steady_clock::time_point m_lastRecoilTime;
    std::chrono::steady_clock::time_point m_recoilStartTime;
    int m_currentBulletIndex;
    bool m_recoilActive;
    bool m_optimizedSequenceSent;

    // Helper methods for keybind monitoring
    bool isKeyPressed(int vkCode) const;
    bool isKeyJustPressed(const std::string& action);
    int getVirtualKeyCode(const std::string& keyName) const;
    void processKeybinds();
    void executeAutoCodeLock();
    void executeAntiAfk();
    void toggleInterface();

    // Removed all calibration systems - using simple template approach
};

} // namespace services
} // namespace octane
