<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}</ProjectGuid>
    <RootNamespace>OctaneRecoilController</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)bin\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)bin\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Configuration)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_CONSOLE;UNICODE;_UNICODE;DEBUG;ALLOW_DEV_TOOLS;DISABLE_SECURITY;ALLOW_DEBUGGER;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <AdditionalIncludeDirectories>$(ProjectDir)include;$(ProjectDir)include\core;$(ProjectDir)include\services;$(ProjectDir)include\models;$(ProjectDir)include\views;$(ProjectDir)include\security;$(ProjectDir)include\utils;$(ProjectDir)packages\Microsoft.Web.WebView2.1.0.2210.55\build\native\include;$(ProjectDir)packages\Microsoft.Windows.ImplementationLibrary.1.0.220201.1\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>user32.lib;gdi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib;$(ProjectDir)WebView2Loader.dll.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_CONSOLE;UNICODE;_UNICODE;PRODUCTION_BUILD;ENABLE_SECURITY;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <AdditionalIncludeDirectories>$(ProjectDir)include;$(ProjectDir)include\core;$(ProjectDir)include\services;$(ProjectDir)include\models;$(ProjectDir)include\views;$(ProjectDir)include\security;$(ProjectDir)include\utils;$(ProjectDir)packages\Microsoft.Web.WebView2.1.0.2210.55\build\native\include;$(ProjectDir)packages\Microsoft.Windows.ImplementationLibrary.1.0.220201.1\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>user32.lib;gdi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib;$(ProjectDir)WebView2Loader.dll.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="src\main.cpp" />
    <ClCompile Include="src\core\Application.cpp" />
    <ClCompile Include="src\config\AppConfig.cpp" />
    <ClCompile Include="src\core\Logger.cpp" />
    <ClCompile Include="src\models\Attachment.cpp" />
    <ClCompile Include="src\models\AttachmentData.cpp" />
    <ClCompile Include="src\models\KeybindSettings.cpp" />
    <ClCompile Include="src\models\RecoilSettings.cpp" />
    <ClCompile Include="src\models\Vector2.cpp" />
    <ClCompile Include="src\models\Weapon.cpp" />
    <ClCompile Include="src\models\WeaponData.cpp" />
    <ClCompile Include="src\security\AdvancedSecurityManager.cpp" />
    <ClCompile Include="src\security\LicenseManager.cpp" />
    <ClCompile Include="src\services\AuthenticationService.cpp" />
    <ClCompile Include="src\services\ConfigurationService.cpp" />
    <ClCompile Include="src\services\DeviceService.cpp" />
    <ClCompile Include="src\services\ESP32Service.cpp" />
    <ClCompile Include="src\services\RecoilControlService.cpp" />
    <ClCompile Include="src\services\SecurityService.cpp" />
    <ClCompile Include="src\services\SoundService.cpp" />
    <ClCompile Include="src\utils\ConfigManager.cpp" />
    <ClCompile Include="src\views\MainWindow.cpp" />
    <ClCompile Include="src\views\MainWindowMessageHandlers.cpp" />
    <ClCompile Include="src\views\MainWindowHtml.cpp" />
    <ClCompile Include="src\views\MainWindowScripts.cpp" />
    <ClCompile Include="src\views\MainWindowStructure.cpp" />
    <ClCompile Include="src\views\MainWindowStyles.cpp" />
    <ClCompile Include="src\views\MainWindowBaseStyles.cpp" />
    <ClCompile Include="src\views\MainPageStyles.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\config\AppConfig.h" />
    <ClInclude Include="include\core\Application.h" />
    <ClInclude Include="include\core\Logger.h" />
    <ClInclude Include="include\models\Attachment.h" />
    <ClInclude Include="include\models\AttachmentData.h" />
    <ClInclude Include="include\models\KeybindSettings.h" />
    <ClInclude Include="include\models\RecoilSettings.h" />
    <ClInclude Include="include\models\Vector2.h" />
    <ClInclude Include="include\models\Weapon.h" />
    <ClInclude Include="include\models\WeaponData.h" />
    <ClInclude Include="include\security\AdvancedSecurityManager.h" />
    <ClInclude Include="include\security\LicenseManager.h" />
    <ClInclude Include="include\services\AuthenticationService.h" />
    <ClInclude Include="include\services\ConfigurationService.h" />
    <ClInclude Include="include\services\DeviceService.h" />
    <ClInclude Include="include\services\ESP32Service.h" />
    <ClInclude Include="include\services\RecoilControlService.h" />
    <ClInclude Include="include\services\SecurityService.h" />
    <ClInclude Include="include\services\SoundService.h" />
    <ClInclude Include="include\utils\ConfigManager.h" />
    <ClInclude Include="include\views\MainWindow.h" />
    <ClInclude Include="include\views\MainWindowBaseStyles.h" />
    <ClInclude Include="include\views\MainPageStyles.h" />
    <ClInclude Include="include\WebView2.h" />
    <ClInclude Include="include\WebView2EnvironmentOptions.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
