#include "views/MainWindowBaseStyles.h"

namespace octane {
namespace views {

std::string MainWindowBaseStyles::getBaseStyles()
{
    return R"(
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0f1419 100%);
            color: #ffffff;
            height: 100vh;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            background: linear-gradient(135deg, rgba(10, 10, 10, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%);
        }

        .title-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 25px;
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(0, 255, 136, 0.1) 100%);
            border-bottom: 2px solid rgba(0, 212, 255, 0.3);
        }

        .title-text {
            font-size: 20px;
            font-weight: 700;
            background: linear-gradient(90deg, #00d4ff 0%, #00ff88 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .license-info {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            font-size: 11px;
            color: rgba(255, 255, 255, 0.7);
        }

        .license-info span {
            margin: 1px 0;
        }

        /* Compact Title Bar */
        .title-bar-compact {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 20px;
            background: rgba(0, 0, 0, 0.2);
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
            min-height: 35px;
        }

        .title-compact {
            font-size: 14px;
            font-weight: 600;
            color: #00d4ff;
        }

        .license-compact {
            display: flex;
            gap: 15px;
            font-size: 11px;
            color: rgba(255, 255, 255, 0.6);
        }

        .license-compact span {
            white-space: nowrap;
        }

        /* Tooltip Styles */
        .tooltip {
            position: relative;
            cursor: help;
        }

        .tooltip::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 11px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s, visibility 0.3s;
            z-index: 1000;
            pointer-events: none;
            border: 1px solid rgba(0, 212, 255, 0.3);
        }

        .tooltip:hover::after {
            opacity: 1;
            visibility: visible;
        }

        .tooltip::before {
            content: '';
            position: absolute;
            bottom: 115%;
            left: 50%;
            transform: translateX(-50%);
            border: 5px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.9);
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s, visibility 0.3s;
            z-index: 1000;
        }

        .tooltip:hover::before {
            opacity: 1;
            visibility: visible;
        }

        .tab-navigation {
            display: flex;
            background: rgba(0, 0, 0, 0.3);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .tab-button {
            flex: 1;
            padding: 15px 20px;
            background: transparent;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
            border-bottom: 3px solid transparent;
        }

        .tab-button:hover {
            background: rgba(0, 212, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
        }

        .tab-button.active {
            background: rgba(0, 212, 255, 0.15);
            color: #ffffff;
            border-bottom-color: #00d4ff;
        }

        .tab-content {
            flex: 1;
            padding: 0;
            display: none;
            overflow-y: auto;
        }

        .tab-content.active {
            display: block;
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 30px;
            max-width: 90vw;
            max-height: 90vh;
            overflow-y: auto;
        }

        /* Form Elements */
        input, select, textarea {
            background: #333;
            border: 1px solid #555;
            color: #fff;
            padding: 8px;
            border-radius: 4px;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #00d4ff;
        }

        button {
            background: #444;
            border: 1px solid #666;
            color: #fff;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.2s;
        }

        button:hover {
            background: #555;
        }

        button.primary {
            background: #00d4ff;
            color: #000;
            border: none;
        }

        button.primary:hover {
            background: #00b8e6;
        }

        /* Utility Classes */
        .hidden {
            display: none !important;
        }

        .text-center {
            text-align: center;
        }

        .mb-10 {
            margin-bottom: 10px;
        }

        .mb-15 {
            margin-bottom: 15px;
        }

        .mb-20 {
            margin-bottom: 20px;
        }

        /* Weapon Keybind Styles */
        .weapon-keybind-row {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .weapon-keybind-dropdown {
            flex: 1;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            color: #fff;
            font-size: 14px;
        }

        .weapon-keybind-dropdown:focus {
            outline: none;
            border-color: #00d4ff;
            box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
        }

        .weapon-keybind-input {
            width: 80px;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            color: #fff;
            text-align: center;
            cursor: pointer;
            font-size: 14px;
        }

        .weapon-keybind-input:focus {
            outline: none;
            border-color: #00d4ff;
            box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
        }
    )";
}

} // namespace views
} // namespace octane
