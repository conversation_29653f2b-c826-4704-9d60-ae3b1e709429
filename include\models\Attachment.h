#pragma once
#include <string>

namespace octane {
namespace models {

/**
 * @brief Represents a weapon attachment with its modifiers
 */
class Attachment {
public:
    /**
     * @brief Modifier structure for attachment effects
     */
    struct Modifier {
        bool enabled;
        float scalar;
        float offset;
        
        Modifier() : enabled(false), scalar(1.0f), offset(0.0f) {}
        Modifier(bool e, float s, float o) : enabled(e), scalar(s), offset(o) {}
    };

    /**
     * @brief Default constructor
     */
    Attachment();

    /**
     * @brief Constructor with all parameters
     */
    Attachment(const std::string& name, int id, 
               const Modifier& recoilMod, const Modifier& repeatDelayMod,
               float fovBias, float fovOffset, float zoomFactor);

    // Getters
    const std::string& getName() const { return m_name; }
    int getId() const { return m_id; }
    const Modifier& getRecoilModifier() const { return m_recoilModifier; }
    const Modifier& getRepeatDelayModifier() const { return m_repeatDelayModifier; }
    float getFovBias() const { return m_fovBias; }
    float getFovOffset() const { return m_fovOffset; }
    float getZoomFactor() const { return m_zoomFactor; }

    // Setters
    void setName(const std::string& name) { m_name = name; }
    void setId(int id) { m_id = id; }
    void setRecoilModifier(const Modifier& modifier) { m_recoilModifier = modifier; }
    void setRepeatDelayModifier(const Modifier& modifier) { m_repeatDelayModifier = modifier; }
    void setFovBias(float fovBias) { m_fovBias = fovBias; }
    void setFovOffset(float fovOffset) { m_fovOffset = fovOffset; }
    void setZoomFactor(float zoomFactor) { m_zoomFactor = zoomFactor; }

    /**
     * @brief Check if this is a scope attachment
     */
    bool isScope() const { return m_zoomFactor > 1.0f; }

    /**
     * @brief Check if this attachment affects recoil
     */
    bool affectsRecoil() const { return m_recoilModifier.enabled; }

    /**
     * @brief Check if this attachment affects fire rate
     */
    bool affectsFireRate() const { return m_repeatDelayModifier.enabled; }

private:
    std::string m_name;
    int m_id;
    Modifier m_recoilModifier;      // Recoil modifier
    Modifier m_repeatDelayModifier; // Fire rate modifier
    float m_fovBias;                // FOV bias for scopes
    float m_fovOffset;              // FOV offset for scopes
    float m_zoomFactor;             // Zoom multiplier
};

} // namespace models
} // namespace octane
